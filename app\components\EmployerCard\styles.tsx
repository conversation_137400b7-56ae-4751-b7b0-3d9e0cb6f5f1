import {Dimensions, Platform, StyleSheet} from 'react-native';
import {BaseColors} from '../../config/theme';
import {FontFamily} from '@config/typography';

const {width} = Dimensions.get('window');
const IOS = Platform.OS === 'ios';

export default StyleSheet.create({
  container: {
    backgroundColor: '#fff',
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
    borderRadius: 10,
    marginTop: 15,
  },
  cardBorder: {
    borderWidth: 1,
    borderRadius: 10,
    borderColor: BaseColors.activeColor,
    marginBottom: 15,
    display: 'flex',
  },
  innerPadding: {
    // padding: 15,
  },
  imageView: {width: 24, height: 24},
  medalIcon: {
    width: '100%',
    height: '100%',
  },
  rowDirection: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 7,
    paddingHorizontal: 14,
  },
  rowDirectionHistory: {
    flexDirection: 'row',
    // alignItems: 'center',
    paddingVertical: 4,
    paddingHorizontal: 5,
  },
  dashedBorder: {
    width: 90,
    height: 90,
    borderRadius: 45,
    borderWidth: 2,
    borderColor: '#f4f2ef',
    justifyContent: 'center',
    alignItems: 'center',
  },
  profileImage: {
    width: 50,
    height: 50,
    borderRadius: 35,
  },
  checkmarkPosition: {
    position: 'absolute',
    right: -11,
    bottom: 4,
  },
  textPadding: {
    paddingHorizontal: 15,
  },
  jobTitle: {
    color: BaseColors.primary,
    fontSize: 18,
    fontWeight: '700',
  },
  confirmText: {
    color: BaseColors.white,
    fontSize: 12,
    textAlign: 'center',
    paddingVertical: 2,
    backgroundColor: BaseColors.primary,
    borderTopRightRadius: 10,
    borderTopLeftRadius: 0,
    borderBottomRightRadius: 10,
    borderBottomLeftRadius: 0,
  },
  userName: {
    paddingVertical: 5,
    fontSize: 12,
    fontWeight: '400',
    color: BaseColors.logoColor,
  },
  ratingRow: {
    flexDirection: 'row',
    alignContent: 'center',
  },
  ratingContainer: {
    backgroundColor: BaseColors.primary,
    paddingVertical: 2,
    borderRadius: 5,
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 10,
  },
  ratingText: {
    color: BaseColors.white,
    paddingLeft: 5,
    fontSize: 13,
    fontWeight: '600',
    paddingBottom: 1,
  },
  reviewCount: {
    color: BaseColors.logoColor,
    paddingLeft: 10,
    fontSize: 13,
    fontWeight: '400',
  },
  skillsContainer: {
    flexDirection: 'row',
    paddingTop: 7,
    paddingBottom: 10,
  },
  skillsView: {
    borderWidth: 1,
    borderColor: BaseColors.primary,
    alignSelf: 'center',
    borderRadius: 5,
    padding: 5,
    marginHorizontal: 3,
    paddingHorizontal: 15,
  },
  skillsTxtSty: {
    color: BaseColors.primary,
    fontSize: 12,
    fontWeight: '400',
  },
  bookmarkContainer: {
    // borderWidth: 1,
    // borderColor: 'transparent',
    // backgroundColor: '#f5faff',
    alignItems: 'flex-start',
    // position: 'absolute',
    // right: 5,
    // padding: 5,
    // paddingRight: 20,
    // marginRight: 20,
    borderRadius: 5,
  },
  infoRow: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 4,
  },
  jobdestxtSty: {
    paddingHorizontal: 10,
    fontSize: 14,
    color: BaseColors.logoColor,
    fontFamily: FontFamily.OpenSansRegular,
    textTransform: 'capitalize',
  },
  timeTxtView: {
    paddingHorizontal: 10,
    fontSize: 14,
    color: BaseColors.logoColor,
    fontFamily: FontFamily.OpenSansRegular,
  },
  salaryRow: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingTop: 5,
  },
  salaryText: {
    color: BaseColors.primary,
    paddingHorizontal: 10,
    fontSize: 14,
    fontWeight: '400',
  },
  imgView: {
    borderRadius: width * 0.0125, // Assuming 5 is about 1.25% of the width
    // borderWidth: width * 0.0025, // Assuming 1 is about 0.25% of the width
    borderColor: BaseColors.secondaryBorder,
    width: width * 0.135, // Assuming 40 is 10% of the width
    height: width * 0.135, // Matching width for a square shape
    alignSelf: 'flex-start',
  },

  imgViewHistory: {
    borderRadius: 40, // Assuming 5 is about 1.25% of the width
    // borderWidth: width * 0.0025, // Assuming 1 is about 0.25% of the width
    borderColor: BaseColors.secondaryBorder,
    width: 40, // Assuming 40 is 10% of the width
    height: 40, // Matching width for a square shape
  },
  desTxtSty: {
    fontSize: 14,
    fontFamily: FontFamily.OpenSansRegular,
    color: BaseColors.logoColor,
    paddingLeft: 4,
    width: '80%',
    paddingTop: IOS ? 3 : 0,
  },
  titleTxtSty: {
    fontSize: 18,
    fontFamily: FontFamily.OpenSansBold,
    color: BaseColors.logoColor,
    paddingBottom: IOS ? 2 : 4,
    minWidth: 0,
    flex: 1,
    lineHeight: IOS ? 20 : 18,
  },
  titleTxtHistorySty: {
    fontSize: 14,
    fontFamily: FontFamily.OpenSansBold,
    color: BaseColors.logoColor,
    // paddingBottom: IOS ? 8 : 7,
  },
  rowStyle: { flex: 1, justifyContent: 'space-between', flexDirection: 'row' },
  txtViewSty: {
    paddingLeft: 10,
    paddingBottom: 5,
    flex: 1,
    // width: '70%', // 53.8% of the screen width
  },
  underlineViewSty: {
    borderBottomWidth: 0.7,
    borderBottomColor: BaseColors.activeColor,
  },
  bodyDataSty: {
    paddingVertical: 2,
    paddingHorizontal: 14,
  },
  bodyDataHistorySty: {
    // paddingVertical: 2,
    // paddingHorizontal: 14,
  },
  DataSty: {
    paddingVertical: 5,
    paddingLeft: 14,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    flex: 1,
  },
  companyViewSty: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  skillsViewSty: {
    flexDirection: 'row',
    alignItems: 'center',
    flexWrap: 'wrap', // Add this to allow wrapping
    rowGap: 8,
    flex: 1,
    overflow: 'hidden',
  },
  skillSty: {
    borderWidth: 1,
    borderColor: 'transparent',
    backgroundColor: BaseColors.skillColor,
    padding: 3,
    borderRadius: 10,
  },
  skillTxtSty: {
    color: BaseColors.skillTxtColor,
    fontSize: 12,
    fontFamily: FontFamily.OpenSansRegular,
    paddingBottom: IOS ? 0 : 4,
    // textTransform: 'capitalize',
  },
  reviewViewSty: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingTop: 5,
  },
  ratingTxtSty: {
    color: BaseColors.logoColor,
    fontFamily: FontFamily.OpenSansRegular,
    paddingHorizontal: 7,
  },
  saleryViewSty: {
    backgroundColor: BaseColors.activeColor,
    padding: 7,
    // width: '27%',
    borderRadius: 10,
    marginRight: 5,
  },
  saleryTXtSty: {
    color: BaseColors.primary,
    fontFamily: FontFamily.OpenSansBold,
    textAlign: 'center',
  },
  bottomViewSty: {
    width: '77%',
  },
  draftView: {
    flexDirection: 'row',
    // padding: 5,
  },
  txtSty: {
    color: '#FF0000',
    textAlign: 'center',
    fontSize: 14,
    fontFamily: FontFamily.OpenSansRegular,
    paddingBottom: 4,
  },
  draftSty: {
    backgroundColor: '#FFB3B3',
    alignSelf: 'center',
    marginHorizontal: 8,
    paddingHorizontal: 5,
    borderRadius: 4,
  },
  customTxtSty: {
    color: BaseColors?.white,
    padding: 3,
    fontSize: 10,
  },
  locationViewSty: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignContent: 'center',
  },
  imageViewSty: {
    height: 20,
    width: 20,
    position: 'absolute',
    bottom: -2,
    right: -2,
  },
  customViewSty: {
    backgroundColor: BaseColors.primary,
    borderRadius: 4,
    alignSelf: 'center',
  },
  seeMoreTxtSty: {
    fontSize: 14,
    fontFamily: FontFamily.OpenSansRegular,
    color: BaseColors.primary,
    textDecorationLine: 'underline',
  },
  decriptionTXtSty: {
    fontSize: 12,
    fontFamily: FontFamily.OpenSansRegular,
    color: BaseColors.greyText,
    lineHeight: 18, // Set a consistent line height
    overflow: 'hidden', // Prevent overflow
    textAlign: 'justify', // Optional for better alignment
  },
  clipBoard: {
    // padding: 10,
    alignItems: 'center',
    // justifyContent: 'center',
    // alignSelf: 'center',
    // marginLeft: 5,
    // paddingLeft: 10,
    paddingVertical: 2,
    // backgroundColor: 'red',
  },
  row: {
    flexDirection: 'row',
    // justifyContent: 'space-between',
    gap: 1,
    marginVertical: 1,
  },
  id: {
    fontSize: 12,
    color: BaseColors.textGrey,
    fontFamily: FontFamily.OpenSansMedium,
  },
});
