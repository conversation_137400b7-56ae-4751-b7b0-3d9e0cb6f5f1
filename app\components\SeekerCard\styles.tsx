import {Dimensions, Platform, StyleSheet} from 'react-native';
import {BaseColors} from '../../config/theme';
import {FontFamily} from '@config/typography';

const {width} = Dimensions.get('window');
const IOS = Platform.OS === 'ios';

export default StyleSheet.create({
  container: {
    backgroundColor: '#fff',
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
    borderRadius: 10,
    marginTop: 15,
  },
  confirmText: {
    color: BaseColors.white,
    fontSize: 12,
    textAlign: 'center',
    paddingVertical: 2,
    backgroundColor: BaseColors.primary,
    borderTopRightRadius: 10,
    borderTopLeftRadius: 0,
    borderBottomRightRadius: 10,
    borderBottomLeftRadius: 0,
  },
  cardBorder: {
    borderWidth: 1,
    borderRadius: 10,
    borderColor: BaseColors.activeColor,
    backgroundColor: BaseColors.white,
    marginBottom: 10,
    width: Dimensions.get('screen').width * 0.8, // Fixed width for horizontal cards
    overflow: 'hidden',
  },
  innerPadding: {
    // padding: 15,
    flex: 1, // Allow inner content to expand
  },
  rowDirection: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    paddingVertical: 7,
    paddingHorizontal: 10,
  },
  flexRow: { flex: 1, flexDirection: 'row'},
  dashedBorder: {
    width: 90,
    height: 90,
    borderRadius: 45,
    borderWidth: 2,
    borderColor: '#f4f2ef',
    justifyContent: 'center',
    alignItems: 'center',
  },
  profileImage: {
    width: 50,
    height: 50,
    borderRadius: 35,
  },
  checkmarkPosition: {
    position: 'absolute',
    right: -11,
    bottom: 4,
  },
  applied: {
    marginHorizontal: 15,
    marginVertical: 10,
  },
  textPadding: {
    paddingHorizontal: 15,
  },
  jobTitle: {
    color: BaseColors.primary,
    fontSize: 18,
    fontWeight: '700',
  },
  imageView: {width: 24, height: 24},
  medalIcon: {
    width: '100%',
    height: '100%',
  },
  userName: {
    paddingVertical: 5,
    fontSize: 12,
    fontWeight: '400',
    color: BaseColors.logoColor,
  },
  ratingRow: {
    flexDirection: 'row',
    alignContent: 'center',
  },
  ratingContainer: {
    backgroundColor: BaseColors.primary,
    paddingVertical: 2,
    borderRadius: 5,
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 10,
  },
  ratingText: {
    color: BaseColors.white,
    paddingLeft: 5,
    fontSize: 13,
    fontWeight: '600',
    paddingBottom: 1,
  },
  reviewCount: {
    color: BaseColors.logoColor,
    paddingLeft: 10,
    fontSize: 13,
    fontWeight: '400',
  },
  skillsContainer: {
    flexDirection: 'row',
    paddingTop: 7,
    paddingBottom: 10,
  },
  skillsView: {
    borderWidth: 1,
    borderColor: BaseColors.primary,
    alignSelf: 'center',
    borderRadius: 5,
    padding: 5,
    marginHorizontal: 3,
    paddingHorizontal: 15,
  },
  skillsTxtSty: {
    color: BaseColors.primary,
    fontSize: 12,
    fontWeight: '400',
  },
  bookmarkContainer: {
    borderWidth: 1,
    borderColor: 'transparent',
    // backgroundColor: '#f5faff',
    alignItems: 'flex-start',
    // position: 'absolute',
    // right: 5,
    // padding: 5,
    borderRadius: 5,
    flexDirection: 'row',
  },
  infoRow: {
    flexDirection: 'row',
    alignItems: 'center',
    // paddingVertical: 5,
  },
  jobdestxtSty: {
    paddingHorizontal: 10,
    fontSize: 14,
    color: BaseColors.logoColor,
    fontFamily: FontFamily.OpenSansRegular,
    // backgroundColor: 'red',
    lineHeight: 17,
    // paddingBottom: IOS ? 0 : 4,
  },
  salaryRow: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingTop: 5,
  },
  salaryText: {
    color: BaseColors.primary,
    paddingHorizontal: 10,
    fontSize: 14,
    fontWeight: '400',
  },
  imgView: {
    borderRadius: width * 0.0125,
    // borderWidth: width * 0.0025,
    borderColor: BaseColors.secondaryBorder,
    width: width * 0.12,
    height: width * 0.12,
    // overflow: 'hidden', // Prevent content overflow
  },
  desTxtSty: {
    fontSize: 14,
    fontFamily: FontFamily.OpenSansRegular,
    color: BaseColors.logoColor,
    paddingLeft: 4,
    // paddingBottom: IOS ? 0 : 4,
    paddingTop: IOS ? 3 : 0,
    width: Dimensions.get('screen').width / 3,
    // flex: 1,
  },
  titleTxtSty: {
    fontSize: 18,
    fontFamily: FontFamily.OpenSansBold,
    color: BaseColors.logoColor,
    // paddingBottom: IOS ? 8 : 2,
    lineHeight: 20,
    flex: 1,
  },
  txtViewSty: {
    paddingLeft: 10,
    paddingRight: 0,
    // paddingBottom: 4,
    // width: width * 0.44,
    flex: 1,
  },
  underlineViewSty: {
    borderBottomWidth: 0.7,
    borderBottomColor: BaseColors.activeColor,
  },
  bodyDataSty: {
    paddingVertical: 5,
    paddingHorizontal: 5,
    width: '100%', // Ensure container width is defined
  },
  center: {
    justifyContent: 'space-between',
  },
  DataSty: {
    paddingVertical: 5,
    // paddingHorizontal: 14,
    // marginHorizontal: 10,
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  skillsRateView: {
    marginVertical: 5,
    // paddingHorizontal: 14,
    marginHorizontal: 10,
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  DataCustomSty: {
    // paddingVertical: 5,
    // paddingHorizontal: 14,
    // marginHorizontal: 10,
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  companyViewSty: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  skillsViewSty: {
    flexDirection: 'row',
    alignItems: 'center',
    flexWrap: 'wrap', // Add this to allow wrapping
    rowGap: 8,
  },
  skillSty: {
    borderWidth: 1,
    borderColor: 'transparent',
    backgroundColor: BaseColors.skillColor,
    padding: 5,
    borderRadius: 10,
  },
  skillTxtSty: {
    color: BaseColors.skillTxtColor,
    fontSize: 12,
    fontFamily: FontFamily.OpenSansRegular,
    paddingBottom: IOS ? 0 : 4,
  },
  reviewViewSty: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  ratingTxtSty: {
    color: BaseColors.logoColor,
    fontFamily: FontFamily.OpenSansRegular,
    paddingHorizontal: 7,
  },
  saleryViewSty: {
    backgroundColor: BaseColors.activeColor,
    padding: 10,
    width: '25%',
    borderTopLeftRadius: 10,
    borderBottomLeftRadius: 10,
  },
  saleryCustomViewSty: {
    backgroundColor: BaseColors.activeColor,
    paddingHorizontal: 6,
    paddingVertical: 6,
    // width: '25%',
    borderRadius: 10,
    // borderBottomLeftRadius: 10,
  },
  saleryTXtSty: {
    color: BaseColors.primary,
    fontFamily: FontFamily.OpenSansBold,
    // paddingBottom: IOS ? 0 : 4,
    textAlign: 'center',
    fontSize: 12,
  },
  levelView: {
    backgroundColor: BaseColors.activeColor,
    padding: 3,
    borderRadius: 5,
    marginHorizontal: 10,
    paddingHorizontal: 7,
  },
  proTxtSty: {
    color: BaseColors.primary,
    fontSize: 12,
    fontFamily: FontFamily.OpenSansRegular,
    paddingBottom: IOS ? 0 : 5,
  },
  decriptionTXtSty: {
    fontSize: 14,
    fontFamily: FontFamily.OpenSansRegular,
    lineHeight: 18, // Set a consistent line height
    overflow: 'hidden', // Prevent overflow
    textAlign: 'justify', // Optional for better alignment
    color: BaseColors.textGrey,
  },
  bottomViewSty: {
    width: Dimensions.get('window').width * 0.6,
  },
  applicationsBtns: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginHorizontal: 5,
    marginVertical: 10,
    gap: 3,
  },
  columnBtns: {
    flexDirection: 'column',
    justifyContent: 'space-between',
    marginHorizontal: 15,
    marginVertical: 10,
    gap: 5,
  },
  applicationswholeBtn: {
    gap: 10,
    marginHorizontal: 15,
    marginBottom: 10,
  },
  paymentProcessingBtn: {
    // backgroundColor: BaseColors.primary,
    width: '100%',
    // borderColor: BaseColors.primary,
    // height: IOS
    //   ? Dimensions.get('screen').height / 23
    //   : Dimensions.get('screen').height / 19,
  },
  approveBtn: {
    backgroundColor: BaseColors.completedColor,
    width: '42%',
    borderColor: BaseColors.completedColor,
    // height: IOS
    //   ? Dimensions.get('screen').height / 23
    //   : Dimensions.get('screen').height / 19,
  },
  approveButton: {
    backgroundColor: BaseColors.completedColor,
    width: '40%',
    justifyContent: 'center',
    paddingBottom: IOS ? 0 : 5,
    borderRadius: 5,
  },
  declineBtn: {
    borderColor: BaseColors.btnRed,
    width: '45%',
  },
  counterBtn: {
    // borderColor: BaseColors.primary,
    // width: '100%',
    flex: 1,
  },
  counterBtnstyl: {
    borderColor: BaseColors.primary,
    width: '40%',
    // flex: 1,
  },
  waitBtnChat: {
    borderColor: BaseColors.borderColor,
    // width: '70%',
  },
  waitBtn: {
    borderColor: BaseColors.borderColor,
    width: '70%',
  },
  declineButton: {
    borderColor: BaseColors.btnRed,
    width: '45%',
    borderWidth: 1,
    paddingVertical: 7,
    paddingBottom: IOS ? 0 : 10,
    borderRadius: 5,
  },
  seeMoreButton: {
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 10,
    backgroundColor: '#f0f0f0',
    borderRadius: 15,
  },
  seeMoreTxt: {
    color: BaseColors.primary,
    fontSize: 14,
    fontWeight: 'bold',
  },
  initialsText: {
    fontSize: 24,
    color: '#fff',
  },
  initialsContainer: {
    width: '100%',
    height: '100%',

    justifyContent: 'center',
    alignItems: 'center',
  },
  row: {
    flexDirection: 'row',
    // justifyContent: 'center',
    alignItems: 'baseline',
  },
  crossMainView: {
    // backgroundColor: BaseColors.backgroundRed,
    paddingHorizontal: 8,
    borderWidth: 1,
    borderColor: BaseColors.red,
    borderRadius: 7,
    // height: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },
  crossView: {
    // backgroundColor: 'transparent',
    // borderWidth: 1,
    borderColor: BaseColors.red,
    borderRadius: 7,
    justifyContent: 'center',
    alignItems: 'center',
    // paddingHorizontal: 5,
    // padding: 2,
  },
});
