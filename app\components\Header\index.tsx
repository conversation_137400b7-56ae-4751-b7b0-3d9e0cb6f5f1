/* eslint-disable react-native/no-inline-styles */
import React from 'react';
import {
  ActivityIndicator,
  Dimensions,
  Text,
  TextStyle,
  SafeAreaView as RNSafeAreaView,
  TouchableOpacity,
  View,
  ViewStyle,
} from 'react-native';
import { CustomIcon } from '../../config/LoadIcons';
import { BaseColors } from '@config/theme';
import Entypo from 'react-native-vector-icons/Entypo';
import { Images } from '@config/images';
import FastImage from 'react-native-fast-image';
import { navigationRef } from '@navigation/NavigationService';
import { useSelector } from 'react-redux';
import FIcon from 'react-native-vector-icons/Feather'; // Replace with your specific icon library if different
import Ionicons from 'react-native-vector-icons/Ionicons';
import styles from './style';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { isIOS } from '@app/utils/CommonFunction';

interface HeaderProps {
  onHeaderPress?: () => void;
  leftIcon?: string;
  onLeftPress?: () => void;
  onRightPress?: () => void;
  title?: string;
  rText?: string;
  rSkip?: string | any;
  onRTextPress?: () => void;
  showLoction?: boolean;
  ContainerSty?: ViewStyle;
  titleSty?: TextStyle;
  leftIconSty?: TextStyle;
  rTxtSty?: TextStyle;
  rightIcons?: any;
  skippedLoader?: boolean;
  titleViewSty?: any;
  chatDetail?: any;
  type?: any;
}

const Header: React.FC<HeaderProps> = props => {
  const {
    ContainerSty,
    onHeaderPress,
    title,
    titleSty,
    leftIcon,
    leftIconSty,
    onLeftPress,
    rText,
    rTxtSty,
    rSkip,
    onRightPress,
    rightIcons,
    skippedLoader,
    titleViewSty,
    chatDetail,
  } = props;

  const { userData } = useSelector((s: any) => s.auth);
  const { badgeCount } = useSelector((s: any) => s.notification);
  const insets = useSafeAreaInsets();

  const renderContent = () => (
    <View style={[styles.mainCon, ContainerSty]}>
      <TouchableOpacity
        activeOpacity={1}
        onPress={onHeaderPress}
        style={styles.rowContainer}>
        {leftIcon ? (
          <TouchableOpacity
            activeOpacity={0.8}
            onPress={onLeftPress}
            style={styles.leftIconStyle}>
            {leftIcon === 'logo' ? (
              <FastImage
                source={Images.shortLogo}
                style={styles.logoStyle}
                resizeMode="contain"
              />
            ) : leftIcon === 'cross' ? (
              <Entypo
                name={leftIcon}
                style={{ ...styles.defaultIconSty, ...leftIconSty }}
                size={26}
              />
            ) : (
              <CustomIcon
                name={leftIcon}
                style={leftIconSty || styles.defaultIconSty}
                size={20}
              />
            )}
          </TouchableOpacity>
        ) : null}
        {!chatDetail ? (
          <>
            <View
              style={[
                styles.titleCon,
                titleViewSty,
                // {width: leftIcon ? '80%' : '100%'},
              ]}>
              <Text numberOfLines={1} style={[styles.titleTxt, titleSty]}>
                {title || ''}
              </Text>
            </View>
            {rSkip ? (
              <TouchableOpacity
                onPress={onRightPress}
                style={{
                  borderWidth: 1,
                  borderColor: BaseColors.primary,
                  paddingVertical: 6,
                  paddingHorizontal: 12,
                  borderRadius: 12,
                  alignItems: 'center',
                  justifyContent: 'center',
                }}>
                {skippedLoader ? (
                  <ActivityIndicator color={BaseColors.primary} />
                ) : (
                  <Text
                    style={{
                      color: BaseColors.primary,
                      fontSize: 14,
                      fontWeight: '700',
                    }}>
                    Skip
                  </Text>
                )}
              </TouchableOpacity>
            ) : (
              <View
                style={{
                  flex: 1,
                  flexDirection: 'row',
                  justifyContent: 'flex-end',
                }}>
                {rightIcons?.map((d: any) => {
                  return (
                    <>
                      <TouchableOpacity
                        onPress={d?.onPress}
                        activeOpacity={0.8}
                        style={d?.wrapStyle}>
                        {rText ? (
                          <Text style={rTxtSty || styles.rTxt}>{rText}</Text>
                        ) : d?.type === 'vectorIcon' ? (
                          <FIcon
                            name="edit-2"
                            size={25}
                            style={d?.rightIconSty || styles.rightIconSty}
                          />
                        ) : d?.type === 'saveIcon' ? (
                          <>
                            {d?.saveLoader ? (
                              <ActivityIndicator color={BaseColors.primary} />
                            ) : (
                              <Ionicons
                                name={d?.icon}
                                size={25}
                                style={d?.rightIconSty || styles.rightIconSty}
                              />
                            )}
                          </>
                        ) : d?.type === 'entypo' ? (
                          <Entypo name={d?.icon} size={20} style={d?.rightIconSty || styles.rightIconSty} />
                        ) : (
                          <CustomIcon
                            name={d?.icon}
                            style={d?.rightIconSty || styles.rightIconSty}
                            // size={25}
                          />
                        )}
                      </TouchableOpacity>
                      {d?.badge ? (
                        <TouchableOpacity
                          style={styles.badgeCount}
                          activeOpacity={0.8}
                          onPress={d?.onPress}>
                          <Text style={styles.badgeStyle}>
                            {Number(d?.badge) > 99 ? '99+' : d?.badge || '0'}
                          </Text>
                        </TouchableOpacity>
                      ) : null}
                      {d?.notiBadge && badgeCount > 0 ? (
                        <TouchableOpacity
                          style={styles.badgeCountLeft}
                          activeOpacity={0.8}
                          onPress={d?.onPress}>
                          <Text style={styles.badgeStyle}>
                            {Number(badgeCount) > 99
                              ? '99+'
                              : badgeCount || '0'}
                          </Text>
                        </TouchableOpacity>
                      ) : null}
                    </>
                  );
                })}
              </View>
            )}
          </>
        ) : (
          <View style={styles.userDetails}>
            <View>
              <FastImage
                source={
                  chatDetail?.imageUrl
                    ? {
                      uri:
                          chatDetail?.imageUrl ||
                          'https://via.placeholder.com/40',
                    }
                    : Images?.user
                }
                style={styles.profilePic}
              />
            </View>
            <TouchableOpacity
              style={{
                width: Dimensions.get('screen').width / 1.5,
              }}
              activeOpacity={0.8}
              onPress={() => {
                navigationRef?.current?.navigate('ApplicantDetails', {
                  applicantData: {},
                  job: {},
                  Id: chatDetail?.jobId,
                  type: 'seekerChat',
                  reviewType: 'reviewbyEmployer',
                  seekerProfile: 'seekerProfile',
                  // type: 'seeker',
                  userId:
                    Number(userData?.id) === Number(chatDetail?.userId)
                      ? chatDetail?.applicantId
                      : chatDetail?.userId,
                });
              }}>
              <Text numberOfLines={1} style={styles.userName}>
                {`${chatDetail?.firstName || '-'} ${
                  chatDetail?.lastName?.charAt(0) || '-'
                }`}

                <Text
                  numberOfLines={1}
                  style={{
                    ...styles.userNameViewText,
                  }}>
                  {chatDetail?.jobTitle
                    ? ` (${chatDetail?.jobTitle || ''})`
                    : ''}
                </Text>
              </Text>
              <View style={{ flexDirection: 'row' }}>
                {chatDetail?.isOnline ? <View style={styles.greenDot} /> : null}
                <Text style={styles.lastSeen}>
                  {chatDetail?.isOnline ? 'Online' : ''}
                </Text>
              </View>
            </TouchableOpacity>
          </View>
        )}
      </TouchableOpacity>
    </View>
  );

  if (isIOS()) {
    return (
      <RNSafeAreaView>
        {renderContent()}
      </RNSafeAreaView>
    );
  }

  // For Android, use View with minimal top padding only when needed for navigation gestures
  return (
    <View style={{ paddingTop: Math.max(insets.top, 0) }}>
      {renderContent()}
    </View>
  );
};

export default Header;
