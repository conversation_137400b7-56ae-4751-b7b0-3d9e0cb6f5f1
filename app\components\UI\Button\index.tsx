/* eslint-disable react-native/no-inline-styles */
import React from 'react';
import PropTypes from 'prop-types';
import {
  TouchableOpacity,
  Text,
  StyleSheet,
  View,
  ActivityIndicator,
  Platform,
  Dimensions,
} from 'react-native';
import { FontFamily } from '@config/typography';
import { Images } from '@config/images';
import FastImage from 'react-native-fast-image';
import { BaseColors } from '@config/theme';
const IOS = Platform.OS === 'ios';
const styles = StyleSheet.create({
  round: {
    borderTopStartRadius: 100,
    borderTopEndRadius: 100,
    borderBottomStartRadius: 100,
    borderBottomEndRadius: 100,
  },
  square: {
    borderTopStartRadius: 5,
    borderTopEndRadius: 5,
    borderBottomStartRadius: 5,
    borderBottomEndRadius: 5,
    borderColor: '#1252A5',
    borderWidth: 1,
  },
  btnContainer: {
    flexDirection: 'row',
    paddingHorizontal: 20,
    paddingVertical: 10,
    justifyContent: 'center',
    alignItems: 'center',
  },
  text: { backgroundColor: 'transparent' },
  shadow: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 4.65,
    elevation: 8,
  },
});

/**
 * Component for Button
 * @function Button
 *
 */
export default function Button(props: {
  children?: any;
  type?: string;
  shape?: string;
  raised?: boolean;
  containerStyle?: object;
  borderStyle?: object;
  loading: boolean;
  loaderColor?: string;
  onPress: any;
  style: any;
  txtStyles?: object;
  disable?: boolean;
  buttonTextStyle?: object;
  disablestyle?: any;
  googleIcon?: boolean;
  IosIcon?: boolean;
  icon?: any;
}) {
  const {
    children,
    type,
    shape,
    raised,
    containerStyle,
    borderStyle,
    loading,
    loaderColor,
    onPress,
    style,
    txtStyles,
    disable,
    buttonTextStyle,
    disablestyle,
    googleIcon,
    IosIcon,
    icon,
    ...rest
  } = props;

  const renderText = () => {
    const textColor =
      type === 'outlined' || type === 'secondary' ? BaseColors.primary : '#fff';
    const textStyles = [
      {
        fontSize: Dimensions.get('screen').width * 0.038,
        color: textColor,
        fontFamily: FontFamily.OpenSansBold,
        letterSpacing: 0.8,
        ...txtStyles,
        textAlign: 'center',
        paddingVertical: IOS ? 2.5 : 0,
      },
      buttonTextStyle,
    ];

    return loading ? (
      <ActivityIndicator
        // style={{marginBottom: 0, paddingVertical: IOS ? 0 : 1.5}}
        animating
        color={loaderColor ? loaderColor : type === 'outlined' ? BaseColors.primary : '#FFF'}
      />
    ) : (
      <Text style={textStyles} numberOfLines={1} ellipsizeMode="tail">
        {children}
      </Text>
    );
  };

  return (
    <TouchableOpacity
      disabled={disable}
      activeOpacity={0.8}
      {...rest}
      onPress={loading ? () => {} : onPress}
      style={[
        disable
          ? {
            overflow: 'hidden',
            ...style,
          }
          : { overflow: 'hidden', ...style },
        shape === 'round'
          ? styles.round
          : shape === 'square'
            ? {
              borderTopStartRadius: 8,
              borderTopEndRadius: 8,
              borderBottomStartRadius: 8,
              borderBottomEndRadius: 8,
              borderColor: '#1252A5',
              ...style,
              // borderWidth: darkmode ? 1 : 0,
            }
            : {},
        type === 'outlined'
          ? {
            backgroundColor: 'transparent',
            borderWidth: 1,
            borderColor: BaseColors.primary,
            ...borderStyle,
            ...style,
            ...disablestyle,
          }
          : {},
        type === 'text'
          ? {
            backgroundColor: BaseColors.primary,
            ...style,
            ...disablestyle,
            borderWidth: 1,
            borderColor: BaseColors.primary,
          }
          : {},
        type === 'primary'
          ? {
            backgroundColor: BaseColors.primary,
            ...style,
            ...disablestyle,
            textAlign: 'center',
          }
          : {},

        type === 'secondary'
          ? {
            backgroundColor: BaseColors.secondary,
            ...style,
          }
          : {},
        style,
        { opacity: disable ? 0.5 : 1 },
      ]}>
      <View style={[styles.btnContainer, containerStyle]}>
        {googleIcon && (
          <View style={{ paddingRight: 10, width: 25, height: 25 }}>
            <FastImage
              resizeMode="contain"
              source={Images.googleLogo}
              style={{ width: '100%', height: '100%' }}
            />
          </View>
        )}
        {IosIcon && (
          <View
            style={{
              paddingRight: 10,
            }}>
            {/* <SvgUri width="22" height="22" source={Images.appleLogo} /> */}
          </View>
        )}
        {icon ? icon : null}
        {renderText()}
      </View>
    </TouchableOpacity>
  );
}

Button.propTypes = {
  type: PropTypes.oneOf(['primary', 'outlined', 'text', 'secondary']),
  shape: PropTypes.oneOf(['round', 'square']),
  raised: PropTypes.bool,
  containerStyle: PropTypes.object,
  borderStyle: PropTypes.object,
  loading: PropTypes.bool,
  onPress: PropTypes.func,
  style: PropTypes.any,
  disable: PropTypes.bool,
  googleIcon: PropTypes.bool,
  IosIcon: PropTypes.bool,
  icon: PropTypes.any,
};

Button.defaultProps = {
  type: 'primary', // "primary"  | "outlined" | "text"| "secondary"
  shape: 'square', // "round"  | "square"
  raised: true, // true | false
  containerStyle: {},
  borderStyle: {},
  loading: false, // true | false
  onPress: () => {},
  style: {},
  disable: false,
  googleIcon: false,
  IosIcon: false,
  icon: '',
};
