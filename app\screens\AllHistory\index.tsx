import React, { useEffect, useState } from 'react';
import {
  ActivityIndicator,
  FlatList,
  RefreshControl,
  ScrollView,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import Header from '@components/Header';
import { BaseColors } from '@config/theme';
import AnimatedView from '@components/AnimatedView';
import BaseSetting from '@config/setting';
import { getApiData } from '@app/utils/apiHelper';
import NoRecord from '@components/NoRecord';
import TransactionHistory from '@components/TransactionHistory';
import styles from '@screens/WalletScreen/styles';
import { translate } from '@language/Translate';
import BankHistory from '@components/BankHistory';
import Button from '@components/UI/Button';
import Toast from 'react-native-simple-toast';
import InsetWrapper from '@components/InsetWrapper';

interface statusInterFace {
  page?: any;
  limit?: any;
  status?: any;
}

export default function AllHistory({ navigation }: any) {
  const [bottomLoading, setBottomLoading] = useState(false);
  const [amountLoader, setAmmountLoader] = useState(false);
  const [refreshing, setRefreshing] = useState(false);
  const [withdrawbottomLoading, setWithdrawBottomLoading] = useState(false);
  const [withdrawrefreshing, setWithdrawRefreshing] = useState(false);
  const [totalWithdrawAmmount, setTotalWithdrawAmmount] = useState('');
  const [withdrawHistory, setWithdrawHistory] = useState<any>({
    data: [],
    pagination: { currentPage: 1, isMore: null },
  });
  const [selectedTab, setSelectedTab] = useState('Harbor History');
  const [statusChange, setStatusChange] = useState('All');
  const [transactions, setTransactions] = useState<any>({
    data: [],
    pagination: { currentPage: 1, isMore: null },
  });
  const { data, pagination } = transactions;
  const [loader, setLoader] = useState(false);
  const [withdrawloader, setWithDrawLoader] = useState(false);

  const loaderSet = (bottomLoad = false, refresh = false, type = false) => {
    if (bottomLoad) {
      setBottomLoading(type);
    } else if (refresh) {
      setRefreshing(type);
    } else {
      setLoader(type);
    }
  };
  const withdrawloaderSet = (
    bottomLoad = false,
    refresh = false,
    type = false,
  ) => {
    if (bottomLoad) {
      setWithdrawBottomLoading(type);
    } else if (refresh) {
      setWithdrawRefreshing(type);
    } else {
      setWithDrawLoader(type);
    }
  };

  async function getTransactions(
    page = 1,
    bottomLoader = false,
    refresh = false,
    type = '',
  ) {
    // if (selectedTab === 'Bank History') {
    //   setTransactions({data: [], pagination: {currentPage: 1, isMore: null}});
    //   return;
    // }
    try {
      loaderSet(bottomLoader, refresh, true);
      const endpoint = BaseSetting.endpoints.transactionHistory;
      let pData: statusInterFace = {
        page: page,
        limit: 12,
        status: String(type).toLowerCase(),
      };
      const response = await getApiData({ endpoint, method: 'GET', data: pData });
      if (response?.status) {
        const d = response?.data?.items;
        setTransactions({
          ...transactions,
          data: bottomLoader ? [...data, ...d] : d || [],
          pagination: response?.data?.pagination,
        });
      }
      loaderSet(bottomLoader, refresh, false);
    } catch (er) {
      loaderSet(bottomLoader, refresh, false);
    }
  }
  async function withdrawAmmount() {
    setAmmountLoader(true);
    try {
      const endpoint = BaseSetting.endpoints.withdrawAmmount;

      const response = await getApiData({ endpoint, method: 'POST' });
      if (response?.status) {
        setAmmountLoader(false);
      } else {
        setAmmountLoader(false);
        Toast.show(response?.message || translate('err'), Toast.SHORT);
      }
    } catch (er) {
      setAmmountLoader(false);
      Toast.show(translate('err'), Toast.SHORT);
    }
  }
  async function getWidrawHistory(
    page = 1,
    bottomLoader = false,
    refresh = false,
  ) {
    try {
      withdrawloaderSet(bottomLoader, refresh, true);
      const endpoint = BaseSetting.endpoints.withdrawHistory;
      let pData: statusInterFace = {
        page: page,
        limit: 12,
      };
      const response = await getApiData({ endpoint, method: 'GET', data: pData });

      if (response?.status) {
        const d = response?.data?.items || [];
        setWithdrawHistory(prev => ({
          data: bottomLoader ? [...prev.data, ...d] : d, // Append new data if bottomLoader is true
          pagination: response?.data?.pagination || {
            currentPage: 1,
            isMore: false,
          },
        }));
        setTotalWithdrawAmmount(response.data.balance.amount);
      } else {
        setWithdrawHistory(prev => ({
          ...prev,
          pagination: { currentPage: 1, isMore: false }, // Reset pagination on failure
        }));
      }

      withdrawloaderSet(bottomLoader, refresh, false);
    } catch (er) {
      console.error('Withdraw history fetch error:', er);
      withdrawloaderSet(bottomLoader, refresh, false);
    }
  }

  useEffect(() => {
    getTransactions(1, false, false, statusChange);
    getWidrawHistory(1, false, false);
  }, [selectedTab, statusChange]);

  const onRefresh = React.useCallback(() => {
    if (!loader) {
      getTransactions(1, bottomLoading, true);
    }
  }, [loader]);

  const ListEndLoader = () => {
    if (!loader && bottomLoading) {
      return <ActivityIndicator color={BaseColors.primary} size={'small'} />;
    }
  };
  const ListEndWithdrawLoader = () => {
    if (!withdrawloader && withdrawbottomLoading) {
      return <ActivityIndicator color={BaseColors.primary} size={'small'} />;
    }
  };

  return (
    <InsetWrapper>
      <View style={styles.container}>
        <Header
          leftIcon="back-arrow"
          title={translate('paymentHistory')}
          onLeftPress={() => navigation.goBack()}
        />
        <AnimatedView>
          <View style={styles.containerSub}>
            {['Harbor History', 'Bank History'].map(tab => (
              <TouchableOpacity
                key={tab}
                style={[styles.tab, selectedTab === tab ? styles.active : {}]}
                onPress={() => setSelectedTab(tab)}>
                <Text
                  style={{
                    ...styles.tabText,
                    color:
                      selectedTab !== tab ? BaseColors.primary : BaseColors.white,
                  }}>
                  {tab}
                </Text>
              </TouchableOpacity>
            ))}
          </View>

          {selectedTab === 'Harbor History' ? (
            <View style={styles.containerSub}>
              {['All', 'Credited', 'Debited'].map(status => (
                <TouchableOpacity
                  key={status}
                  style={[
                    styles.tab,
                    status === statusChange ? styles.active : {},
                  ]}
                  onPress={() => setStatusChange(status)}>
                  <Text
                    style={{
                      ...styles.tabText,
                      color:
                        status !== statusChange
                          ? BaseColors.primary
                          : BaseColors.white,
                    }}>
                    {status}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          ) : null}

          <View style={styles.container}>
            {selectedTab === 'Bank History' ? (
              <View style={{ marginHorizontal: 20 }}>
                {/* <ScrollView
                  style={{marginHorizontal: 20, marginBottom: 10}}
                  // refreshControl={
                  //   <RefreshControl
                  //     refreshing={refreshing}
                  //     onRefresh={onRefresh}
                  //     colors={[BaseColors.primary]}
                  //     tintColor={BaseColors.primary}
                  //   />
                  // }
                  showsVerticalScrollIndicator={false}> */}
                {/* {withdrawloader ? (
                    <View style={styles.centerMain}>
                      <ActivityIndicator color={BaseColors.primary} />
                    </View>
                  ) : ( */}

                <View style={[styles.row, { alignItems: 'center' }]}>
                  <Text style={styles.titleTxtSty}>
                    {translate('totalWithdrawAmmount', '')} -{' '}
                    {totalWithdrawAmmount}
                  </Text>
                  {totalWithdrawAmmount > '0' ? (
                    <Button
                      loading={amountLoader}
                      onPress={withdrawAmmount}
                      containerStyle={{
                        paddingHorizontal: 15,
                        paddingVertical: 5,
                      }}
                      txtStyles={{ fontSize: 14 }}
                      type="text">
                      {translate('withdraw', '')}
                    </Button>
                  ) : null}
                </View>
                <FlatList
                  data={withdrawHistory?.data || []}
                  style={{ marginHorizontal: 7 }}
                  contentContainerStyle={{}}
                  keyExtractor={(item: any) => `${item.id}+1`}
                  renderItem={({ item }) => (
                    <View>
                      <BankHistory item={item} navigation={navigation} />
                    </View>
                  )}
                  ListEmptyComponent={
                    <View style={styles.centerMain}>
                      <NoRecord title={'noData'} iconName="employer" />
                    </View>
                  }
                  showsVerticalScrollIndicator={false}
                  ListFooterComponent={ListEndWithdrawLoader}
                  onEndReachedThreshold={0.1}
                  onEndReached={() => {
                    if (withdrawHistory?.pagination?.isMore) {
                      getWidrawHistory(
                        Number(withdrawHistory.pagination.currentPage) + 1,
                        true,
                      );
                    }
                  }}
                  ItemSeparatorComponent={() => <View style={styles.separator} />}
                />
                {/* )} */}
                {/* </ScrollView> */}
              </View>
            ) : (
              <ScrollView
                style={{ marginHorizontal: 20, marginBottom: 10 }}
                refreshControl={
                  <RefreshControl
                    refreshing={refreshing}
                    onRefresh={onRefresh}
                    colors={[BaseColors.primary]}
                    tintColor={BaseColors.primary}
                  />
                }
                showsVerticalScrollIndicator={false}>
                {loader ? (
                  <View style={styles.centerMain}>
                    <ActivityIndicator color={BaseColors.primary} />
                  </View>
                ) : (
                  <FlatList
                    data={transactions?.data || []}
                    style={{ marginHorizontal: 7 }}
                    contentContainerStyle={{ flex: 1 }}
                    keyExtractor={(item: any) => `${item.id}+1`}
                    renderItem={({ item }) => (
                      <TransactionHistory item={item} navigation={navigation} />
                    )}
                    ListEmptyComponent={
                      <View style={styles.centerMain}>
                        <NoRecord title={'noData'} iconName="employer" />
                      </View>
                    }
                    showsVerticalScrollIndicator={false}
                    ListFooterComponent={ListEndLoader}
                    onEndReachedThreshold={0.1}
                    onEndReached={() => {
                      if (pagination?.isMore === true) {
                        getTransactions(Number(pagination.currentPage) + 1, true);
                      }
                    }}
                    ItemSeparatorComponent={() => (
                      <View style={styles.separator} />
                    )}
                  />
                )}
              </ScrollView>
            )}
          </View>
        </AnimatedView>
      </View>
    </InsetWrapper>
  );
}
