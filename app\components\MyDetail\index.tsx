// Upload.js
import React, { useRef, useState } from 'react';
import {
  Keyboard,
  Platform,
  ScrollView,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import styles from './styles';
import TextInput from '../UI/TextInput';
import { translate } from '@language/Translate';
import ImagePicker from 'react-native-image-crop-picker';
import { isValidPhoneNumber } from 'libphonenumber-js';
import Placeautocomplete from '@components/Placeautocomplete';
import authActions from '@redux/reducers/auth/actions';

import {
  chatFilesVal,
  formatDate,
  getFormattedAddress,
  handleFilePick,
} from '@app/utils/CommonFunction';
import { Images } from '@config/images';
import { BaseColors } from '@config/theme';
import DropdownList from '@components/DropDownList';
import Button from '@components/UI/Button';
import AIcon from 'react-native-vector-icons/AntDesign';
import { isArray, isEmpty, isNull, startCase } from '@app/utils/lodashFactions';
import Icon from 'react-native-vector-icons/Fontisto';
import EIcon from 'react-native-vector-icons/Entypo';
import RBSheet from 'react-native-raw-bottom-sheet';
import ActionSheet from 'react-native-actionsheet';
import FastImage from 'react-native-fast-image';
import AnimatedView from '@components/AnimatedView';
import { getApiData } from '@app/utils/apiHelper';
import BaseSetting from '@config/setting';
import { useAppDispatch, useRedux } from '@components/UseRedux';
import Toast from 'react-native-simple-toast';

// Define the type for dropdown items
interface DropdownItem {
  title: string;
}

interface NewObj {
  firstName?: string;
  lastName?: string;
  email?: string;
  phoneNumber?: string;
  birthDate?: any;
  company?: string;
  gender?: string;
  location?: string;
  countryCode?: string;
  profilePhoto?: any;
  cv?: any; // Optional because it depends on `cvFile`
  coordinates?: any;
  flagCode?: string;
  invitedReferralCode?: string;
  isAvailable?: any;
  licenses?: string | Array<any>; // Adjust the type of `licenseFiles` accordingly
  shortAddress: any;
  formatedAddress: any;
  setFormatedAddresss: any;
  gettingFlagCode: any;
  skillIds: any;
  about: any;
  selectedTags: any;
}
export default function MyDetail({
  onNext,
  selectedGender,
  setSelectedGender,
  name,
  setName,
  firstNameErr,
  setFirstNameErr,
  firstNameErrTxt,
  lastNameErr,
  setLastNameErr,
  lastNameErrTxt,
  lastname,
  setLastName,
  phoneNumber,
  setPhoneNumber,
  phoneNumberError,
  setPhoneNumberError,
  phoneNumberErrorTxt,
  setPhoneNumberErrorTxt,
  companyName,
  setCompanyName,
  companyNameErr,
  setCompanyNameErr,
  companyNameErrTxt,
  email,
  setEmail,
  genderErr,
  setGenderError,
  emailErr,
  setEmailErr,
  genderErrTxt,
  location,
  setLocation,
  locationErr,
  setLocationErr,
  locationErrTxt,
  selectedDate,
  setSelectedDate,
  setEmailErrTxt,
  emailErrTxt,
  selectDateErrTxt,
  selectDateErr,
  setSelectDateErr,
  setSelectDateErrTxt,
  countryCode,
  setCountryCode,
  setFlagCode,
  flagCode,
  profileImage,
  setProfileImage,
  params,
  setImageName,
  setFirstNameErrTxt,
  setLastNameErrTxt,
  setCompanyNameErrTxt,
  Review,
  setReferralCode,
  referralCode,
  cvFile,
  licenseFiles,
  setLicenseFiles,
  setFormatedAddresss,
  formatedAddress,
  gettingFlagCode,
  skillIds,
  about,
  selectedTags,
}: any) {
  const { useAppSelector } = useRedux();
  const dispatch = useAppDispatch();
  const { userData } = useAppSelector((state: any) => state.auth); // Use your RootState type
  const IOS = Platform.OS === 'ios';
  const CANCEL_INDEX = 2;
  const DESTRUCTIVE_INDEX = 0;
  const genderOptions: DropdownItem[] = [
    { title: 'Male' },
    { title: 'Female' },
    { title: 'Other' },
  ];

  // Calculate the minDate as 10 years before the current date
  const tenYearsAgo = new Date();
  tenYearsAgo.setFullYear(tenYearsAgo.getFullYear() - 0);

  const today = new Date();
  const yesterday = new Date();
  yesterday.setDate(today.getDate() - 1);

  const validateEmailFormat = (email: string): boolean => {
    const emailRegex =
      /^(([^<>()[\]\\.,;:\s@\"]+(\.[^<>()[\]\\.,;:\s@\"]+)*)|(\".+\"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
    return emailRegex.test(email);
  };
  const validatePhoneNumber = (number: string, code: string) => {
    const fullPhoneNumber = `+${code}${number}`;
    return isValidPhoneNumber(fullPhoneNumber);
  };

  const isPhoneNumberDisable = isNull(userData?.appleId) && isNull(userData?.googleId);

  const formattedDate = selectedDate ? formatDate(selectedDate) : ''; // Format the date
  const [submitLoader, setSubmitLoader] = useState<boolean>(false);

  const handleSubmit = async () => {
    setSubmitLoader(true);
    const newObj: NewObj = {
      firstName: name || undefined,
      lastName: lastname || undefined,
      email: email || undefined,
      phoneNumber: phoneNumber || undefined,
      birthDate: formattedDate || undefined,
      company: companyName || '',
      gender: selectedGender ? selectedGender.toLowerCase() : undefined,
      location: location?.description || '',
      countryCode: `+${countryCode}` || undefined,
      flagCode: flagCode,
      skillIds: !isEmpty(selectedTags) ? selectedTags : undefined,
      shortAddress: formatedAddress,
      coordinates: {
        lat: location?.lat || '',
        long: location?.long || '',
      },
      about: about,
    };
    if (referralCode) {
      newObj.invitedReferralCode = referralCode;
    }

    newObj.profilePhoto =
      profileImage && profileImage?.includes('http')
        ? profileImage?.split('/').pop()
        : profileImage || '';

    newObj.cv = (() => {
      if (typeof cvFile === 'string') {
        if (cvFile.includes('http')) {
          // Extract the file name from the URL
          return cvFile.split('/').pop();
        } else {
          // Return the string directly if it doesn't contain 'http'
          return cvFile;
        }
      } else if (cvFile && typeof cvFile === 'object') {
        // Return the fileName if cvFile is an object
        return cvFile.fileName;
      }
      return ''; // Fallback if cvFile is neither string nor valid object
    })();

    const updatedLicenseFiles = licenseFiles
      .map((file: any) => {
        if (typeof file === 'string') {
          // If the file is a URL, extract the file name
          return file.split('/').pop();
        } else if (file?.fileName) {
          // If the file is an object, use the fileName property
          return file.fileName;
        }
        return null;
      })
      .filter(Boolean); // Filter out any null values

    // Update the licenseFiles state with the extracted file names
    setLicenseFiles(updatedLicenseFiles);

    if (!isEmpty(updatedLicenseFiles) && isArray(updatedLicenseFiles)) {
      newObj.licenses = updatedLicenseFiles.map(file => {
        console.log('Current file:', file); // Log the current file for debugging

        if (typeof file === 'string') {
          // Case 1: When `licenseFiles` is an array of URLs (strings)
          if (file.includes('http')) {
            const fileName = file.split('/').pop();
            console.log('Extracted file name:', fileName);
            return fileName; // Extract file name from URL
          } else {
            console.log('Returning file as is:', file);
            return file; // Return as is
          }
        } else if (typeof file === 'string') {
          return file;
        } else if (typeof file === 'object' && file?.fileName) {
          // Case 2: When `licenseFiles` is an array of objects
          console.log('Returning fileName from object:', file.fileName);
          return file.fileName; // Extract fileName from the object
        } else {
          console.warn('Unhandled file format:', file);
          return null; // Return null if the format is unknown
        }
      });
    }

    try {
      const res = await getApiData({
        endpoint: BaseSetting.endpoints.updateUser,
        method: 'POST',
        data: newObj,
      });
      if (res?.status === true) {
        // Toast.show(res?.message || translate('err', ''), Toast.BOTTOM);
        setSubmitLoader(false);
        dispatch(authActions.setUserData(res?.data));
        dispatch(authActions.setUserProfileData(res?.data));
        onNext();
      } else {
        console.log('error', res);
        setSubmitLoader(false);
        Toast.show(res?.message || translate('err', ''), Toast.BOTTOM);
      }
    } catch (err) {
      console.log('check Error');
      setSubmitLoader(false);

      // Toast.show(translate('err', ''), Toast.BOTTOM);
    }
  };

  // const formattedDate = selectedDate ? formatDate(selectedDate) : ''; // Format the date

  const validateFields = () => {
    let isValid = true;
    const code =
      typeof countryCode === 'object'
        ? countryCode.callingCode[0]
        : countryCode;
    if (!name.trim()) {
      setFirstNameErr(true);
      setFirstNameErrTxt(translate('firstNameRequired', ''));
      isValid = false;
    } else if (name.length < 2) {
      setFirstNameErr(true);
      setFirstNameErrTxt(translate('nameLimit', ''));
      isValid = false;
    } else {
      setFirstNameErr(false);
    }
    if (String(companyName).trim()) {
      if (String(companyName).length < 3) {
        isValid = false;
        setCompanyNameErr(true);
        setCompanyNameErrTxt(translate('leastCharacters', ''));
      } else if (String(companyName).length > 100) {
        isValid = false;
        setCompanyNameErr(true);
        setCompanyNameErrTxt(translate('maximumCharacters', ''));
      } else if (!/^[A-Za-z\s]+$/.test(companyName)) {
        isValid = false;
        setCompanyNameErr(true);
        setCompanyNameErrTxt(translate('alphabeticCharacters', ''));
      } else {
        setCompanyNameErr(false);
      }
    } else {
      setCompanyNameErr(false);
    }

    if (!lastname.trim()) {
      setLastNameErr(true);
      setLastNameErrTxt(translate('lastNameReuired', ''));

      isValid = false;
    } else if (lastname?.length < 2) {
      setLastNameErr(true);
      setLastNameErrTxt(translate('minCharater', ''));
      isValid = false;
    } else {
      setLastNameErr(false);
    }

    if (phoneNumber === '') {
      setPhoneNumberError(true);
      setPhoneNumberErrorTxt(translate('eneterPhone', ''));
      isValid = false;
    } else if (!validatePhoneNumber(phoneNumber, code)) {
      setPhoneNumberError(true);
      setPhoneNumberErrorTxt(translate('validPhoneNumber', ''));
      isValid = false;
    } else {
      setPhoneNumberError(false);
      setPhoneNumberErrorTxt('');
    }

    if (!email.trim()) {
      setEmailErr(true);
      setEmailErrTxt(translate('emailRequired', ''));
      isValid = false;
    } else if (!validateEmailFormat(email)) {
      setEmailErr(true);
      setEmailErrTxt(translate('validemailAddress', ''));
      isValid = false;
    } else {
      setEmailErr(false);
    }

    if (!location?.description?.trim()) {
      setLocationErr(true);
      isValid = false;
    } else {
      setLocationErr(false);
    }

    if (!selectedGender) {
      setGenderError(true);
      isValid = false;
    } else {
      setGenderError(false);
    }
    if (!selectedDate) {
      setSelectDateErr(true);
      isValid = false;
    } else {
      setSelectDateErr(false);
    }
    return isValid;
  };

  const handleNextPress = () => {
    if (validateFields()) {
      handleSubmit();
    }
  };
  const handleDateChange = (date: Date) => {
    setSelectDateErrTxt('');
    setSelectDateErr(false);
    setSelectedDate(date);
  };

  const handleComplete = (val, e) => {
    // Call getFormattedAddress to get city and state
    const formattedAddress = getFormattedAddress(e);

    // Set the location state with the description and formatted address
    setLocation(p => ({
      ...p,
      description: val?.description || '',
      lat: e?.geometry?.location?.lat,
      long: e?.geometry?.location?.lng,
    }));
    setFormatedAddresss(formattedAddress);
  };

  // Refs for each TextInput
  const nameRef = useRef<any>(null);
  const Lnameref = useRef<any>(null);
  const Phoneref = useRef<any>(null);
  const companyRef = useRef<any>(null);
  const dateRef = useRef<any>(null);
  const genderRef = useRef<any>(null);
  const Emailref = useRef<any>(null);
  const locationRef = useRef<any>(null);
  const referralRef = useRef<any>(null);

  const ActionSheetRef = useRef<any>();
  const ActionSheetRefIOS = useRef<any>();
  function showActionSheet() {
    if (IOS) {
      ActionSheetRefIOS.current.open();
    } else {
      ActionSheetRef.current.show();
    }
  }

  function doAction(index: any) {
    if (index === 0) {
      openGallery(index);
    } else if (index === 1) {
      openCamera();
    }
  }

  const options = [
    <TouchableOpacity
      onPress={() => openGallery('type')}
      key={`gallery-option-${1}`}
      style={[styles.optionsContainer, { marginTop: IOS ? 15 : 0 }]}>
      <Icon name="picture" size={18} color={BaseColors.primary} />
      <Text
        style={{
          marginLeft: 15,
          color: BaseColors.primary,
        }}>
        {translate('Gallery', '')}
      </Text>
    </TouchableOpacity>,
    <TouchableOpacity
      onPress={() => openCamera('type')}
      key={`camera-option-${2}`}
      style={[styles.optionsContainer, { paddingVertical: 10, marginLeft: 6 }]}>
      <Icon name="camera" size={18} color={BaseColors.primary} />
      <Text style={{ marginLeft: 15, color: BaseColors.primary }}>
        {translate('Camera', '')}
      </Text>
    </TouchableOpacity>,
    <TouchableOpacity
      onPress={() => {
        if (IOS) {
          ActionSheetRefIOS.current.close();
        } else {
          ActionSheetRef.current.hide();
        }
      }}
      key={`cancel-option-${3}`}
      style={[
        styles.optionsContainer,
        {
          paddingVertical: 10,
          marginHorizontal: IOS ? 0 : 20,
          borderTopWidth: IOS ? 3 : 0,
          borderTopColor: BaseColors.textInput,
        },
      ]}>
      <EIcon name="cross" size={18} color={BaseColors.primary} />

      <Text style={{ marginLeft: 15, color: BaseColors.primary }}>
        {translate('Cancel', '')}
      </Text>
    </TouchableOpacity>,
  ];

  //  function for openGallery
  const openGallery = async () => {
    try {
      const image: any = await ImagePicker.openPicker({
        cropping: true,
      });

      const fType = image?.mime || '';
      const isValidFile = chatFilesVal(fType, image.size);

      if (isValidFile) {
        // Pass the selected file and type to handleFilePick
        const fileRes = await handleFilePick(image, 'image');
        setProfileImage(fileRes?.data?.filePath);
        setImageName(image?.path);
        if (IOS) {
          ActionSheetRefIOS.current.close();
        } else {
          ActionSheetRef.current.hide();
        }
      } else {
        setTimeout(() => {
          // Toast.show(
          //   'Please select a valid file or file size must not be exceeded',
          //   Toast.BOTTOM,
          // );
        }, 50);
      }
    } catch (error) {
      console.error('Error picking image:', error);
    }
  };

  // this is for open camara..
  const openCamera = async () => {
    try {
      const image: any = await ImagePicker.openCamera({
        cropping: true,
      });

      const fType = image?.mime || '';
      const isValidFile = chatFilesVal(fType, image.size);

      if (isValidFile) {
        // Pass the selected file and type to handleFilePick
        const fileRes = await handleFilePick(image, 'image');
        setProfileImage(fileRes?.data?.filePath);
        setImageName(image?.path);
        if (IOS) {
          ActionSheetRefIOS.current.close();
        } else {
          ActionSheetRef.current.hide();
        }
      } else {
        setTimeout(() => {
          // Toast.show(translate('appliedValidSizeFile', ''), Toast.BOTTOM);
        }, 50);
      }
    } catch (error) {
      console.error('Error picking image:', error);
    }
  };

  return (
    <>
      <View style={{ flex: 1, justifyContent: 'space-between' }}>
        <ScrollView
          // keyboardDismissMode="on-drag"
          showsVerticalScrollIndicator={false}
          keyboardShouldPersistTaps="handled">
          <AnimatedView>
            <View style={styles.basicDetailView}>
              {/* <Text style={styles.detailTxt}>{translate('basicDetail', '')}</Text> */}
              <View style={styles.container}>
                <View style={styles.imgViewContainer}>
                  <TouchableOpacity
                    style={styles.photoContainer}
                    onPress={() =>
                      Review === 'reviewType' ? null : showActionSheet()
                    }>
                    <View style={styles.dashedBorder}>
                      {!profileImage ? (
                        <AIcon
                          name="camera"
                          size={30}
                          color={BaseColors.primary}
                        />
                      ) : (
                        <FastImage
                          source={
                            profileImage
                              ? { uri: profileImage }
                              : Images.introscreenThree
                          }
                          style={styles.profileImage}
                          resizeMode="cover"
                        />
                      )}
                    </View>
                  </TouchableOpacity>
                  <View style={{ width: '72%' }}>
                    <Text numberOfLines={2} style={styles.text}>
                      {Review === 'reviewType'
                        ? translate('updatePhoto', '')
                        : translate('setPhoto', '')}
                    </Text>
                  </View>
                </View>
              </View>
              <View style={styles.firstNameViewSty}>
                <View>
                  <TextInput
                    style={{
                      borderColor: BaseColors.white,
                      backgroundColor: '#f5faff',
                      opacity: 1,
                    }}
                    title={translate('firstName', '')}
                    placeholderText={translate('firstName', '')}
                    value={name}
                    showError={firstNameErr}
                    errorText={firstNameErrTxt}
                    onChange={(value: any) => {
                      setName(value);
                      if (value.length > 0) {
                        setFirstNameErr(false);
                      }
                    }}
                    maxChar={15}
                    returnKeyType="next"
                    ref={nameRef}
                    onSubmit={() => {
                      Lnameref?.current?.focus();
                    }}
                    iseditable={Review === 'reviewType' ? false : true}
                    Review={'reviewType'}
                  />
                </View>
                <View style={styles?.mView}>
                  <TextInput
                    maxChar={15}
                    style={{
                      borderColor: BaseColors.white,
                      backgroundColor: '#f5faff',
                      opacity: 1,
                    }}
                    title={translate('lastName', '')}
                    placeholderText={translate('lastName', '')}
                    value={lastname}
                    showError={lastNameErr}
                    errorText={lastNameErrTxt}
                    onChange={(value: any) => {
                      setLastName(value);
                      if (value.length > 0) {
                        setLastNameErr(false);
                      }
                    }}
                    returnKeyType="next"
                    ref={Lnameref}
                    onSubmit={() => {
                      Phoneref.current.focus();
                    }}
                    iseditable={Review === 'reviewType' ? false : true}
                    Review={'reviewType'}
                  />
                </View>
                <View style={styles.mBottom}>
                  <TextInput
                    style={{
                      borderColor: BaseColors.white,
                      backgroundColor:
                        Review === 'reviewType' ? '#f5faff' : '#f5faff',
                    }}
                    phoneNumber={true}
                    value={phoneNumber}
                    title={translate('phone', '')}
                    placeholderText={translate('phone', '')}
                    showError={phoneNumberError}
                    errorText={phoneNumberErrorTxt}
                    ref={Phoneref}
                    onSubmit={() => {
                      companyRef.current.focus();
                    }}
                    callingCode={countryCode}
                    countryCode={
                      flagCode
                        ? String(flagCode)?.toUpperCase()
                        : gettingFlagCode
                    }
                    iseditable={!isPhoneNumberDisable}
                    onChange={(txt: string) => {
                      const cleanedTxt = txt.trim().replace(/[^\d]/g, '');
                      setPhoneNumber(cleanedTxt);
                      if (validatePhoneNumber(cleanedTxt, countryCode)) {
                        setPhoneNumberError(false);
                        setPhoneNumberErrorTxt('');
                      } else {
                        setPhoneNumberError(true);
                        setPhoneNumberErrorTxt(
                          'Please enter a valid phone number',
                        );
                      }
                    }}
                    onCountryChange={(codeObj: any) => {
                      const code = codeObj.callingCode[0];
                      setCountryCode(code);
                      setFlagCode(codeObj?.cca2);
                      if (validatePhoneNumber(phoneNumber, code)) {
                        setPhoneNumberError(false);
                        setPhoneNumberErrorTxt('');
                      } else {
                        setPhoneNumberError(true);
                        setPhoneNumberErrorTxt(
                          'Please enter a valid phone number',
                        );
                      }
                    }}
                    containerSty={!phoneNumberError && styles.noErrorContainer}
                    titleSty={styles.inputTitle}
                    textInputStyle={styles.textInput}
                    codeTxtSty={styles.codeText}
                    keyBoardType="number-pad"
                    returnKeyType="next"
                    Review={'reviewType'}
                  />
                </View>
              </View>
            </View>

            <View style={styles.otherDetailViewSty}>
              <View style={styles.mtopSty}>
                <TextInput
                  maxChar={100}
                  style={{
                    borderColor: BaseColors.white,
                    backgroundColor: '#f5faff',
                    opacity: 1,
                  }}
                  title={translate('company', '')}
                  placeholderText={translate('typecompanyName', '')}
                  value={companyName}
                  onChange={(value: any) => {
                    const trimmedValue = value.startsWith(' ')
                      ? value.trimStart()
                      : value;
                    setCompanyName(trimmedValue);
                    if (trimmedValue.length > 0) {
                      setCompanyNameErr(false);
                    }
                  }}
                  showError={companyNameErr}
                  errorText={companyNameErrTxt}
                  returnKeyType="next"
                  ref={companyRef}
                  onSubmit={() => {
                    Keyboard.dismiss();
                  }}
                  maxLength={100}
                  iseditable={Review === 'reviewType' ? false : true}
                  Review={'reviewType'}
                />
              </View>
              <View
                style={[
                  styles?.mView,
                  {
                    marginBottom: 15,
                  },
                ]}>
                <TextInput
                  Date={true} // Enables date picker in CInput
                  title={translate('dateofBirth', '')}
                  selectedDate={selectedDate} // Set initially selected date
                  onDateChange={handleDateChange} // Handle date selection changes
                  placeholderText={translate('selectDate', '')}
                  errorText={selectDateErrTxt} // Optional: Error message
                  DateError={selectDateErr} // Set to true if there's a date error
                  datetimemodal="Select Date"
                  ref={dateRef}
                  maxDate={yesterday} // Set minDate as 10 days ago
                  onSubmit={() => {
                    genderRef.current.focus();
                  }}
                  iseditable={Review !== 'reviewType'}
                  Review={'reviewType'}
                  style={{
                    backgroundColor: '#f5faff',
                    opacity: 1,
                  }}
                  textStyle={{
                    color: BaseColors.primary,
                  }}
                />
              </View>
              <View>
                <DropdownList
                  title={translate('gender', '')}
                  data={genderOptions}
                  selectedValue={startCase(selectedGender)}
                  onSelect={(value: string) => {
                    setSelectedGender(value);
                    if (value?.length > 0) {
                      setGenderError(false);
                    }
                  }}
                  placeholder={translate('selectGender', '')}
                  showError={genderErr}
                  errorTxt={genderErrTxt}
                  ref={genderRef}
                  disable={Review === 'reviewType' ? true : false}
                />
              </View>
              <View style={{ paddingTop: 15 }}>
                <TextInput
                  style={{
                    borderColor: BaseColors.white,
                    backgroundColor:
                      Review === 'reviewType' ? '#f5faff' : '#f5faff',
                  }}
                  title={translate('Email', '')}
                  value={email}
                  showError={emailErr}
                  errorText={emailErrTxt}
                  onChange={(value: string) => {
                    setEmail(value);
                    if (value.length > 0) {
                      setEmailErr(false);
                    }
                  }}
                  i={params === 'edit' ? false : true}
                  returnKeyType="next"
                  placeholderText={translate('Email', '')}
                  ref={Emailref}
                  onSubmit={() => {
                    locationRef.current.focus();
                  }}
                  // iseditable={!isEmpty(userData?.email) ? false : true}
                  iseditable={isPhoneNumberDisable}
                  Review={'reviewType'}
                />
              </View>
              <View style={{ marginTop: 15, marginBottom: 5 }}>
                <Placeautocomplete
                  refs={locationRef}
                  isError={locationErr}
                  isErrorMsg={locationErrTxt}
                  onAutoCompleteAddressSelect={(data, details) =>
                    handleComplete(data, details)
                  }
                  setLocation={setLocation}
                  placeholder={translate('currentLocation', '')}
                  location={location?.description}
                  isDisable={Review === 'reviewType' ? true : false}
                  title={translate('location', '')}
                />
              </View>
              {params?.type !== 'edit' ? (
                <View style={styles.mtopSty}>
                  <TextInput
                    maxChar={35}
                    style={styles.input}
                    title={translate('referralCode', '')}
                    placeholderText={translate('referralCode', '')}
                    value={referralCode}
                    onChange={(value: any) => {
                      const trimmedValue = value.startsWith(' ')
                        ? value.trimStart()
                        : value;
                      setReferralCode(trimmedValue);
                    }}
                    returnKeyType="next"
                    ref={referralRef}
                    onSubmit={() => {
                      Keyboard.dismiss();
                    }}
                    maxLength={35}
                  />
                </View>
              ) : null}
            </View>

            <ActionSheet
              ref={ActionSheetRef}
              options={options}
              cancelButtonIndex={CANCEL_INDEX}
              destructiveButtonIndex={DESTRUCTIVE_INDEX}
              onPress={(index: any) => doAction(index)}
            />
            <RBSheet
              ref={ActionSheetRefIOS}
              closeOnDragDown={true}
              closeOnPressMask={true}
              dragFromTopOnly={true}
              height={180}
              customStyles={{
                draggableIcon: {
                  width: 50,
                  marginTop: 30,
                },
                container: {
                  backgroundColor: '#FFF',
                  borderTopRightRadius: 20,
                  borderTopLeftRadius: 20,
                },
              }}>
              <View>
                {options?.map(item => {
                  return item;
                })}
              </View>
            </RBSheet>
          </AnimatedView>
        </ScrollView>
        {Review === 'reviewType' ? null : (
          <View style={styles.nextBtnSty}>
            <Button
              loading={submitLoader}
              type="text"
              onPress={handleNextPress}>
              {translate('next', '')}
            </Button>
          </View>
        )}
      </View>
    </>
  );
}
