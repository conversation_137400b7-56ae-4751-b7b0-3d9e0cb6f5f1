import appsFlyer from 'react-native-appsflyer';
import { Platform } from 'react-native';
import BaseSetting from '@config/setting';
import { trackPurchaseEvent } from './trackPurchaseEvent';
import { PERMISSIONS, PermissionStatus, request } from 'react-native-permissions';
import messaging from '@react-native-firebase/messaging';
// import { getApiData } from './apiHelper';
// import { navigationRef } from '@navigation/NavigationService';
import authActions from '@redux/reducers/auth/actions';
// import AsyncStorage from '@react-native-async-storage/async-storage';
import { store } from '@redux/store/configureStore';
import ReactNativeIdfaAaid from '@sparkfabrik/react-native-idfa-aaid';

type ReferralLinkType = 'referral' | 'activity' | 'provider_profile_link' | string;

export const createReferralLink = (
  user_id: string | number,
  type: ReferralLinkType = 'referral',
  activity_id: string | number | null = null,
  business_id: string | number | null = null,
  near_id: string | number | null = null
): Promise<any> => {
  const onLinkId =
    type === 'activity'
      ? 'A04V'
      : type === 'provider_profile_link'
        ? 'ttD2'
        : 'kHn0';

  return new Promise((resolve, reject) => {

    appsFlyer.setAppInviteOneLinkID(onLinkId, (res: unknown) => {
      if (res === 'Success') {
        const params: any = {
          channel: 'social',
          campaign: 'Referral Link',
          customerID: user_id,
          userParams: {
            af_sub1: user_id,
            af_sub2: type,
            af_sub3: activity_id,
            af_sub4: business_id,
            af_sub5: near_id,
            deep_link_value: user_id,
            deep_link_sub1: type,
            af_dp: 'happykamper://app',
          },
        };

        if (type === 'activity') {
          params.campaign = 'Activity Link';
        } else if (type === 'provider_profile_link') {
          params.campaign = 'Provider Profile Link';
        }

        appsFlyer.generateInviteLink(
          params,
          (successRes: any) => resolve(successRes),
          (errorRes: any) => reject(errorRes)
        );
      } else {
        reject('Failed to set AppInviteOneLinkID');
      }
    });
  });
};

const initAppsFlyer = (): void => {
  const options = {
    devKey: BaseSetting.APPFLYER_DEV_KEY,
    isDebug: false,
    onInstallConversionDataListener: true,
    onDeepLinkListener: true,
    appId: Platform.OS === 'ios' ? 'id6746108776' : '',
    timeToWaitForATTUserAuthorization: 10,
  };

  const initAppsFlyerSdk = (): void => {
    appsFlyer.initSdk(
      options,
      (result: any) => {
        console.log('AppsFlyer SDK initialized successfully:', result);

        appsFlyer.getAppsFlyerUID((error: any, deviceID: string) => {
          const device_id = store.getState().auth.device_id;
          ReactNativeIdfaAaid?.getAdvertisingInfo()
            .then((res: { id: string | null }) => {
              const id = res.id ?? '';
              if (device_id !== id) {
              // store.dispatch({
              //   type: authActions.SET_USER_DEVICE_ID,
              //   device_id: id,
              // });
              }
            })
            .catch((err: any) => {
              console.log(err);
            // store.dispatch({
            //   type: authActions.SET_USER_DEVICE_ID,
            //   device_id: '',
            // });
            });


          if (error) {
            console.error('Error retrieving device ID:', error);
          } else {
            console.log('AppsFlyer Device ID:', deviceID);
          }
        });
      },
      (error: any) => {
        console.error('AppsFlyer SDK initialization failed:', error);
      }
    );
  };

  if (Platform.OS === 'ios') {
    request(PERMISSIONS.IOS.APP_TRACKING_TRANSPARENCY).then((res: PermissionStatus) => {
      if (res === 'granted') {
        setTimeout(() => {
          initAppsFlyerSdk();
          store.dispatch({
            type: authActions.SET_IS_TRACK_PERMISSION,
            isTrackPermission: true,
          });
        }, 1000);
      } else {
        store.dispatch({
          type: authActions.SET_IS_TRACK_PERMISSION,
          isTrackPermission: false,
        });
        console.log('App tracking transparency permission not granted');
      }
    });
  } else {
    setTimeout(() => {
      initAppsFlyerSdk();
    }, 1000);
  }

  messaging().onTokenRefresh((token: string) => {
    appsFlyer.updateServerUninstallToken(token, () => {
      trackPurchaseEvent('af_uninstall_app', { UNINSTALL_APP: token });
    });
  });

  appsFlyer.onInstallConversionData(async (res: any) => {
    if (res) {
      const data = res.data;
      if (data.af_status === 'Non-organic') {
        // const resp1 = await getApiData(
        //   BaseSetting.endpoints.referralData,
        //   'POST',
        //   { ...res, type: 'Referral-1' },
        //   {},
        //   false
        // );

        // const sub1 = resp1?.data?.data?.data?.deep_link_sub1;
        // if (sub1 === 'referral' || sub1 === 'activity' || sub1 === 'affiliate') {
        //   store.dispatch({
        //     type: authActions.SET_REFERRAL_DATA,
        //     referrerData: resp1?.data?.data?.data,
        //   });
        // }

        const userID = data.user_id;
        console.log('User ID from Referral:', userID);
      } else {
        console.log('Install Type:', data.af_status);
      }
    }
  });
};

appsFlyer.onDeepLink(async (res: any) => {
  console.log('🚀 ~ appsFlyer.onDeepLink ~ res:', res);

  if (res.deepLinkStatus === 'FOUND') {
    // const resp3 = await getApiData(
    //   BaseSetting.endpoints.referralData,
    //   'POST',
    //   { ...res, type: 'DeepLink-1' },
    //   {},
    //   false
    // );

    // const userType = await AsyncStorage.getItem('userType');
    // const data = resp3?.data?.data?.data;

    // if (data?.activity_id && data?.deep_link_sub1 === 'activity' && userType === 'parent') {
    //   setTimeout(() => {
    //     navigationRef.current?.navigate('ActivityDetailsList', {
    //       data: {
    //         id: Number(data.activity_id),
    //         business_id: Number(data.business_id),
    //         near_id: data.near_id ? Number(data.near_id) : null,
    //         form: 'Parent',
    //       },
    //     });
    //   }, 1500);
    // }

    // if (data?.deep_link_value && data?.deep_link_sub1 === 'category' && userType === 'parent') {
    //   const categoryData = store.getState().auth?.mainPageCatData;
    //   const category = categoryData?.find((item: any) => item?.id === Number(data.deep_link_value));

    //   setTimeout(() => {
    //     navigationRef.current?.navigate('ListingPageActivity', {
    //       data: {
    //         type: category?.name ?? data?.af_sub4,
    //         id: Number(data.af_sub1),
    //         image: category?.image_url ?? data?.af_sub5,
    //       },
    //     });
    //   }, 1500);
    // }

    // if (data?.deep_link_value && data?.deep_link_sub1 === 'sub_category' && userType === 'parent') {
    //   const categoryData = store.getState().auth?.mainPageCatData;
    //   const category = categoryData?.find((item: any) => item?.id === Number(data.af_sub1));

    //   setTimeout(() => {
    //     navigationRef.current?.navigate('ListingPageActivity', {
    //       data: {
    //         type: category?.name ?? data?.af_sub4,
    //         id: Number(data.af_sub1),
    //         image: category?.image_url ?? data?.af_sub5,
    //         sub_category_id: Number(data.af_sub3),
    //       },
    //     });
    //   }, 1500);
    // }

    // if (data?.business_id && data?.type === 'provider_profile_link' && userType === 'parent') {
    //   setTimeout(() => {
    //     navigationRef.current?.navigate('ProviderDetail', {
    //       data: {
    //         business_id: data.business_id,
    //         type: 'parent',
    //       },
    //     });
    //   }, 1500);
    // }

    // if (data?.deep_link_sub1 === 'activity') {
    //   store.dispatch({
    //     type: authActions.SET_REFERRAL_DATA,
    //     referrerData: { ...data, ...res.data },
    //   });
    // }

    // if (data?.af_link_type === 'message_inbox') {
    //   setTimeout(() => {
    //     navigationRef.current?.navigate('InboxTabStack');
    //   }, 1500);
    // }
  } else {
    console.warn('Deep Link Not Found');
  }
});

export default initAppsFlyer;
