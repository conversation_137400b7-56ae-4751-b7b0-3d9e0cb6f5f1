import {BaseColors} from '@config/theme';
import {FontFamily} from '@config/typography';
import {Dimensions, StyleSheet} from 'react-native';
const {width} = Dimensions.get('window');

export default StyleSheet.create({
  container: {
    backgroundColor: BaseColors.white,
    flex: 1,
    // marginBottom: 10,
    paddingBottom: 20,
  },
  imgView: {
    borderRadius: width * 0.0125, // Assuming 5 is about 1.25% of the width
    borderWidth: width * 0.0025, // Assuming 1 is about 0.25% of the width
    borderColor: BaseColors.secondaryBorder,
    width: width * 0.12, // Assuming 40 is 10% of the width
    height: width * 0.12, // Matching width for a square shape
  },
  crossMainView: {
    // backgroundColor: BaseColors.backgroundRed,
    paddingHorizontal: 8,
    borderWidth: 1,
    borderColor: BaseColors.red,
    borderRadius: 7,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },
  approveBtn: {
    backgroundColor: BaseColors.completedColor,
    // width: '45%',
    borderColor: BaseColors.completedColor,
    // height: IOS
    //   ? Dimensions.get('screen').height / 23
    //   : Dimensions.get('screen').height / 19,
  },
  skillView: {
    borderWidth: 1,
    //   padding: 7,
    marginHorizontal: 5,
    borderColor: BaseColors.primary,
    borderRadius: 5,
    paddingHorizontal: 10,
  },
  row: {
    flexDirection: 'row',
    // justifyContent: 'space-around', // Space between items
    alignSelf: 'center', // Align items vertically
    padding: 5,
    // marginTop: 10,
  },
  rowCenter: {
    alignSelf: 'center',
    flex: 1,
    justifyContent: 'center',
  },
  skillText: {
    fontSize: 16,
    fontFamily: FontFamily.OpenSansRegular,
    color: BaseColors.primary,
    paddingBottom: 4,
  },
  dateText: {
    fontFamily: FontFamily.OpenSansRegular,
    color: '#000',
  },
  applicantsLink: {
    textDecorationLine: 'underline',
    color: BaseColors.primary,
    fontFamily: FontFamily.OpenSansRegular,
    marginLeft: 5,
  },
  skillsRow: {
    flexDirection: 'row',
    justifyContent: 'center',
    marginVertical: 10,
  },
  skillBadge: {
    borderWidth: 1,
    borderColor: BaseColors.primary,
    borderRadius: 5,
    paddingHorizontal: 10,
    marginHorizontal: 5,
  },
  skillText: {
    color: BaseColors.primary,
    fontFamily: FontFamily.OpenSansRegular,
    paddingBottom: 5,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingHorizontal: 15,
    marginTop: 20,
  },
  sectionTitle: {
    fontFamily: FontFamily.OpenSansMedium,
    fontSize: 18,
    color: BaseColors.textColor,
  },
  seeAll: {
    color: BaseColors.primary,
    fontFamily: FontFamily.OpenSansMedium,
    fontSize: 14,
  },
  centerMain: {
    flex: 1,
    alignContent: 'center',
    justifyContent: 'center',
    display: 'flex',
    // marginTop: HEIGHT / 7,
  },
  applicantsList: {
    paddingHorizontal: 15,
    marginTop: 10,
    marginBottom: 12,
  },
  cardContainer: {
    width: Dimensions.get('screen').width * 0.41,
    // height: 'auto',
    flex: 1,
    backgroundColor: '#fff',
    borderRadius: 10,
    marginRight: 15,
    padding: 10,
    borderWidth: 0.5,
    borderColor: BaseColors.black,
  },
  profileImage: {
    width: 50,
    height: 50,
    borderRadius: 25,
    alignSelf: 'center',
  },
  cardInfo: {
    marginTop: 10,
    alignItems: 'center',
  },
  cardName: {
    fontFamily: FontFamily.OpenSansBold,
    fontSize: 14,
    color: '#000',
  },
  cardSubtitle: {
    fontFamily: FontFamily.OpenSansRegular,
    fontSize: 12,
    color: '#666',
  },
  ratingRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 5,
  },
  rating: {
    fontFamily: FontFamily.OpenSansRegular,
    fontSize: 12,
    color: '#000',
  },
  proBadge: {
    backgroundColor: BaseColors.activeColor,
    color: BaseColors.primary,
    fontSize: 10,
    borderRadius: 5,
    paddingHorizontal: 10,
    marginLeft: 5,
    padding: 3,
  },
  levelBadge: {
    backgroundColor: BaseColors.activeColor,
    color: BaseColors.primary,
    fontSize: 10,
    borderRadius: 5,
    paddingHorizontal: 5,
    marginLeft: 5,
    paddingTop: 3,
  },
  section: {
    paddingHorizontal: 15,
    paddingTop: 10,
  },
  imageRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  imagePlaceholder: {
    width: 60,
    height: 60,
    backgroundColor: '#f5f5f5',
    borderRadius: 5,
  },
  descriptionText: {
    fontFamily: FontFamily.OpenSansMedium,
    fontSize: 14,
    color: BaseColors?.textColor,
    marginTop: 10,
  },
  imageWrapper: {
    position: 'relative',
    marginRight: 10,
    marginBottom: 10,
  },
  uploadedImage: {
    width: Dimensions.get('screen').width * 0.25,
    height: Dimensions.get('screen').height / 8,
    borderRadius: 5,
  },
  imagesRow: {
    flexDirection: 'row',
    flexWrap: 'nowrap',
    marginTop: 10,
  },
  msgIconStyle: {
    right: 0,
    width: 35,
    height: 35,
    // borderWidth: 1,
    borderRadius: 10,
    justifyContent: 'center',
    alignItems: 'center',
    borderColor: BaseColors.primary,
  },

  pagination: {flexDirection: 'row', justifyContent: 'center', marginTop: 10},
  paginationDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: '#ccc',
    marginHorizontal: 4,
  },
  activeDot: {backgroundColor: BaseColors.primary},
  seekerSty: {
    color: BaseColors?.textColor,
    fontFamily: FontFamily.OpenSansMedium,
    fontSize: 18,
  },
  seekerDesSty: {
    fontSize: 14,
    fontFamily: FontFamily.OpenSansRegular,
  },
  seeMoreTxt: {
    color: BaseColors?.primary,
    fontFamily: FontFamily.OpenSansMedium,
    fontSize: 14,
    textTransform: 'capitalize',
  },
  reviewView: {
    marginTop: 10,
    // flexDirection: 'row',
    // flexWrap: 'wrap',
  },
  txtView: {
    // width: '55%',
  },
  ratingView: {
    // width: '45%',
    alignSelf: 'flex-end',
  },
  borderLine: {
    borderBottomWidth: 1,
    borderBottomColor: BaseColors.bordrColor,
    marginTop: 15,
  },
  descriptionReviewSty: {
    fontSize: 14,
    fontFamily: FontFamily?.OpenSansRegular,
    color: BaseColors?.textColor,
    lineHeight: 20, // Add this property
  },
  imageViewSty: {
    width: 25,
    height: 25,
    borderRadius: 5,
    borderWidth: 1,
    borderColor: BaseColors.bordrColor,
    marginRight: 10,
  },
  txtSty: {
    fontSize: 16,
    paddingHorizontal: 10,
    fontFamily: FontFamily?.OpenSansMedium,
  },
  mainReviewView: {
    flexDirection: 'row',
    // alignItems: 'center',
    justifyContent: 'space-between',
  },
  mainImgView: {
    flexDirection: 'row',
    // alignItems: 'center',
  },
  imgSty: {
    width: '100%',
    height: '100%',
    borderRadius: 30,
  },
  timeView: {
    // alignContent: 'center',
    alignSelf: 'flex-end',
  },
  decscriptionView: {
    flexDirection: 'column',
    marginTop: 5,
    alignItems: 'flex-start',
  },
  mainReviewSty: {
    flexDirection: 'column',
  },

  errView: {
    paddingTop: 6,
  },
  checkboxErr: {
    fontSize: 14,
    color: BaseColors.red,
    fontFamily: FontFamily.OpenSansRegular,
    // alignSelf: 'center',
    marginHorizontal: 35,
  },
  txtnavigationSty: {
    color: BaseColors.primary,
    borderBottomColor: BaseColors.primary,
    borderBottomWidth: 1,
    // textAlign: 'center',
    // backgroundColor: 'pink',
  },
  clickSty: {
    borderWidth: 1,
    borderColor: BaseColors.primary,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 4,
    height: 17,
    width: 17,
  },
  checkboxView: {
    paddingTop: 10,
    alignContent: 'center',
    // justifyContent: 'center',
    flexDirection: 'row',
    marginHorizontal: 15,
  },
  txtaggrementView: {
    flexDirection: 'row',
    flexWrap: 'wrap', // Allow text to wrap to the next line
    alignItems: 'center', // Vertically align items
  },
  mView: {
    flex: 1, // Allow the container to take available width
    flexWrap: 'wrap',
  },

  seeMoreText: {
    fontSize: 14,
    color: BaseColors.primary,
    textDecorationLine: 'underline',
  },
  reviewViewSty: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingTop: 5,
  },
  ratingTxtSty: {
    color: BaseColors.logoColor,
    fontFamily: FontFamily.OpenSansRegular,
    paddingHorizontal: 7,
  },
  declinedStatus: {
    marginHorizontal: 45,
    paddingHorizontal: 10,
    paddingVertical: 5,
    borderWidth: 0.6,
    borderRadius: 5,
    borderColor: BaseColors.red,
    justifyContent: 'center',
    alignItems: 'center',
    opacity: 0.5,
  },
  declineText: {
    fontSize: 14,
    fontFamily: FontFamily.OpenSansBold,
    color: BaseColors.red,
  },
  infoView: {
    marginHorizontal: 20,
    paddingHorizontal: 5,
    paddingVertical: 5,
    borderWidth: 0.6,
    borderRadius: 5,
    borderColor: BaseColors.primary,
    justifyContent: 'center',
    alignItems: 'flex-start',
    opacity: 0.8,
    marginBottom: 5,
  },
  infoText: {
    fontSize: 12,
    fontFamily: FontFamily.OpenSansBold,
    color: BaseColors.primary,
  },
  marginBt: {
    marginTop: 15,
    marginBottom: 10,
    marginHorizontal: 20,
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  paginationStyle: {
    position: 'absolute', // Ensures that dots appear at the bottom
    top: 515, // Adjust position if necessary
    left: 0,
    right: 0,
    justifyContent: 'center',
    // backgroundColor: 'blue',
  },
  skillsView: {
    borderWidth: 1,
    borderColor: 'transparent',
    backgroundColor: BaseColors.skillColor,
    paddingVertical: 5,
    borderRadius: 10,
    paddingHorizontal: 10,
  },
  skillTxtSty: {
    color: BaseColors.skillTxtColor,
    fontSize: 14,
    fontFamily: FontFamily.OpenSansRegular,
  },
});
