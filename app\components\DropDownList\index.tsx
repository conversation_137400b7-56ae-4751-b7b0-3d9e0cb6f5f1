/* eslint-disable react-native/no-inline-styles */
import React from 'react';
import PropTypes from 'prop-types';
import {Text, StyleSheet, View, Platform, Dimensions} from 'react-native';
import {Dropdown, MultiSelect} from 'react-native-element-dropdown';

import {useTheme} from '@react-navigation/native';
import {BaseColors} from '../../config/theme';
import {FontFamily} from '@config/typography';
import {CustomIcon} from '@config/LoadIcons';
import {isEmpty} from '@app/utils/lodashFactions';
const IOS = Platform.OS === 'ios';

const styles = StyleSheet.create({
  titleText: {
    paddingBottom: 8,
    fontSize: 16,
    fontFamily: FontFamily.OpenSansRegular,
  },
  dropdown: {
    borderRadius: 15,
    paddingHorizontal: 8,
    height: 50,
    marginBottom: 2,
    width: '100%',
  },
  placeholderStyle: {
    fontSize: 14,
    color: BaseColors.dividerColor,
    fontFamily: FontFamily.OpenSansRegular,
  },
  selectedTextStyle: {
    fontSize: 14,
  },
  selectedTextStyleInmulti: {
    fontSize: 14,
  },
  iconStyle: {
    width: 25,
    height: 20,
  },
  inputSearchStyle: {
    height: 40,
    fontSize: 16,
  },
  errTxt: {
    fontSize: 13,
    color: '#d62828',
    paddingBottom: 5,
    paddingTop: IOS ? 5 : 0,
    marginHorizontal: 4,
    fontFamily: FontFamily.OpenSansRegular,
  },
  textStyle: {
    fontSize: 13,
  },
  containerStyle: {
    borderWidth: 0,
    marginTop: 0,
    marginLeft: 0.5,
    borderRadius: 15,
    borderColor: BaseColors.white,
    height: Dimensions.get('window').height / 4.2,
    elevation: 2,
  },
  item: {
    padding: 10,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
});

/**
 * Component for Dropdown
 * @function Dropdown
 *
 */
export default function DropdownList(props: {
  mandatory: boolean;
  showError: any;
  data: any;
  selectedValue: any;
  errorTxt: any;
  title: any;
  onSelect: any;
  placeholder: any;
  textStyle: any;
  search: any;
  style: any;
  multipleSelection?: any;
  selectedItems?: any;
  setSelectedItems?: any;
  viewstyle?: any;
  searchPlaceholder?: any;
  containerStyle?: any;
  defaultSelected?: any;
  disable?: any;
  ref?: any;
  maxSelect?: any;
  fromVoice?: any;
  noRightIcon?: boolean;
}) {
  const {
    showError,
    data,
    selectedValue,
    errorTxt,
    title,
    onSelect,
    placeholder,
    textStyle,
    mandatory,
    search,
    style,
    multipleSelection,
    selectedItems,
    setSelectedItems,
    viewstyle,
    searchPlaceholder,
    containerStyle,
    defaultSelected,
    disable,
    maxSelect,
    fromVoice,
    noRightIcon,
    ref,
  } = props;
  const modifiedData = !isEmpty(data)
    ? data?.map((item: any) => ({
        ...item,
        selected: item.title === defaultSelected,
      }))
    : [];
  const IOS = Platform.OS === 'ios';
  const colors = useTheme();
  const BaseColors: any = colors.colors;

  return (
    <View style={{...style}}>
      {title && (
        <Text
          style={{
            ...styles.titleText,
            ...textStyle,
            color: BaseColors.inputColor,
          }}>
          {title || ''}
          {mandatory && (
            <Text
              style={{
                fontSize: 14,
                color: BaseColors.inputColor,
              }}>
              {'*'}
            </Text>
          )}
        </Text>
      )}
      {multipleSelection ? (
        <MultiSelect
          disable={disable}
          ref={ref}
          style={{
            ...styles.dropdown,
            borderColor: showError ? '#d62828' : BaseColors.white20,
            borderWidth: 1,
            color: BaseColors.text,
            backgroundColor: disable
              ? BaseColors.lightGreyColor
              : BaseColors.repingInner,
            ...viewstyle,
          }}
          itemContainerStyle={{
            borderTopColor: search ? undefined : BaseColors.white40,
            borderTopWidth: 0,
            borderBottomColor: search ? BaseColors.white40 : undefined,
            borderBottomWidth: search ? 0.8 : 0,
            borderRadius: 10,
          }}
          placeholderStyle={{
            ...styles.placeholderStyle,
            color: BaseColors.black,
          }}
          iconColor={BaseColors.text}
          containerStyle={{
            ...styles.containerStyle,
            borderTopColor: BaseColors.text,
            backgroundColor: BaseColors.sheet,
            ...containerStyle,
          }}
          selectedTextStyle={{
            ...styles.selectedTextStyleInmulti,
            color: BaseColors.text,
          }}
          activeColor={BaseColors.primary}
          iconStyle={styles.iconStyle}
          itemTextStyle={{...styles.textStyle, color: BaseColors.text}}
          data={defaultSelected ? modifiedData : data}
          labelField="title"
          valueField="title"
          placeholder={placeholder}
          maxSelect={maxSelect}
          // searchField={search}
          searchPlaceholder={searchPlaceholder}
          value={selectedItems}
          inputSearchStyle={{
            borderRadius: 10,
            borderColor: BaseColors.white30,
            color: BaseColors.text,
            fontSize: 14,
            fontFamily: FontFamily.OpenSansRegular,
            height: 40,
            marginBottom: 0,
          }}
          onChange={(items: any) => {
            setSelectedItems(items);
            onSelect(items.map((item: any) => item));
          }}
          showsVerticalScrollIndicator={false}
          // inside={true}
          selectedStyle={{
            borderRadius: 10,
            borderColor: BaseColors.primary,
            borderWidth: 1,
            padding: 5,
          }}
          search={search}
        />
      ) : (
        <Dropdown
          ref={ref}
          style={{
            ...styles.dropdown,
            borderColor: 'transparent',
            borderWidth: fromVoice ? 0 : 1,
            backgroundColor: disable
              ? BaseColors.lightGreyColor
              : showError
              ? '#FFF2F1'
              : '#f5faff',
            color: BaseColors.text,
            ...viewstyle,
          }}
          itemContainerStyle={{
            borderTopColor: search ? undefined : BaseColors.white40,
            borderTopWidth: 0,
            borderBottomColor: search ? BaseColors.white40 : '#AAAAAA',
            borderBottomWidth: search ? 0.8 : 1,
            borderRadius: 10,
          }}
          placeholderStyle={{
            ...styles.placeholderStyle,
            color: showError
              ? BaseColors.errorUpdatetxt
              : BaseColors.dividerColor,
            paddingHorizontal: 6,
            paddingBottom: 5,
          }}
          iconColor={showError ? BaseColors.errorUpdatetxt : BaseColors.primary}
          containerStyle={{
            ...styles.containerStyle,
            borderTopColor: BaseColors.text,
            backgroundColor: BaseColors.white,
            ...containerStyle,
          }}
          selectedTextStyle={{
            ...styles.selectedTextStyle,
            color: BaseColors.primary,
            fontFamily: FontFamily.OpenSansRegular,
            paddingBottom: IOS ? 0 : 5,
            paddingHorizontal: 6,
          }}
          activeColor={BaseColors.lightBlack}
          iconStyle={styles.iconStyle}
          itemTextStyle={{...styles.textStyle, color: BaseColors.text}}
          data={data}
          labelField="title"
          valueField="title"
          placeholder={fromVoice ? selectedValue : placeholder}
          // searchField={search}
          value={selectedValue}
          showsVerticalScrollIndicator={false}
          onChange={(item: any) => {
            onSelect(item.title);
          }}
          disable={disable}
          searchPlaceholder={searchPlaceholder}
          search={search}
          inputSearchStyle={{
            borderRadius: 10,
            borderColor: BaseColors.white30,
            color: BaseColors.text,
            fontSize: 14,
            fontFamily: FontFamily.OpenSansRegular,
            height: 40,
            marginBottom: 0,
          }}
          renderItem={item => {
            const isActive = item.title === selectedValue; // Check if the item is selected
            return (
              <View
                style={{
                  borderTopColor: search ? undefined : BaseColors.white40,
                  borderTopWidth: 0,
                  padding: IOS ? 20 : 15,
                  flexDirection: 'row',
                  justifyContent: 'space-between',
                  alignItems: 'center',
                }}>
                <Text
                  style={{
                    color: isActive
                      ? BaseColors.primary
                      : BaseColors.dropdownTxtColor, // Conditional color
                    fontFamily: FontFamily.OpenSansRegular,
                  }}>
                  {item.title}
                </Text>
              </View>
            );
          }}
          renderRightIcon={() => noRightIcon ? null : (
            <CustomIcon
              name="ArrowUp" // Change to any icon name
              size={24}
              color={BaseColors.primary}
              style={{
                paddingRight: 5,
                transform: [{rotate: '180deg'}], // Rotate the icon
              }}
            />
          )}
        />
      )}
      {showError && <Text style={styles.errTxt}>{showError && errorTxt}</Text>}
    </View>
  );
}

DropdownList.propTypes = {
  showError: PropTypes.bool,
  data: PropTypes.array,
  selectedValue: PropTypes.string,
  errorTxt: PropTypes.string,
  title: PropTypes.string,
  onSelect: PropTypes.func,
  placeholder: PropTypes.string,
  textStyle: PropTypes.object,
  mandatory: PropTypes.bool,
  search: PropTypes.bool,
  style: PropTypes.object,
};

DropdownList.defaultProps = {
  showError: false,
  data: [],
  selectedValue: '',
  errorTxt: '',
  title: '',
  onSelect: () => {},
  placeholder: '',
  textStyle: {},
  mandatory: false,
  search: false,
  style: {},
};
