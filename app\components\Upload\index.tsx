import React, { useEffect, useRef, useState } from 'react';
import { View, Text, TouchableOpacity } from 'react-native';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import DocumentPicker from 'react-native-document-picker';
import styles from './styles';
import Button from '../UI/Button';
import { translate } from '@language/Translate';
import {
  formatDate,
  handleFilePick,
  maxFileSize,
} from '@app/utils/CommonFunction';
import { isEmpty } from '@app/utils/lodashFactions';
import AnimatedView from '@components/AnimatedView';
import UploadData from '@components/UploadData';
import ExperienceCard from '@components/ExperinceCard';
import CertificateCard from '@components/CertificateCard';
import CvFileComponent from '@components/CvFileComponant';
import LicenseFileList from '@components/LicenseFileList';
import { useDispatch, useSelector } from 'react-redux';
import { getApiData } from '@app/utils/apiHelper';
import BaseSetting from '@config/setting';
import authActions from '@redux/reducers/auth/actions';
import { Platform } from 'react-native';
import ImagePicker from 'react-native-image-crop-picker';
import ImageCropPicker from 'react-native-image-crop-picker';
import { smartAlbums } from '@config/staticdata';

interface NewObj {
  skillIds?: Array<number | string>; // Adjust based on the actual type of `selectedTags`
  about?: string; // Optional because it uses `data?.about`
  firstName?: string;
  lastName?: string;
  email?: string;
  phoneNumber?: string;
  birthDate?: any;
  company?: string;
  gender?: string;
  location?: string;
  countryCode?: string;
  profilePhoto?: any;
  licenses?: string | Array<any>; // Adjust the type of `licenseFiles` accordingly
  cv?: any; // Optional because it depends on `cvFile`
  coordinates?: any;
  flagCode?: string;
  navigation: any;
  setUploadLicenseFile: any;
  uploadLicenseFile: any;
  shortAddress: any;
  setAddData: any;
  addData: any;
  ActionSheetRef: any;
  ActionSheetRefIOS: any;
  uploadLicense: any;
  setUploadLicene: any;
  selectedTags: any;
}

export default function Upload({
  onNext,
  setIsUploadValid,
  setCvError,
  setLicenseError,
  licenseError,
  cvError,
  setCvFile,
  cvFile,
  setLicenseFiles,
  licenseFiles,
  deleteFile,
  Review,
  // Added state fields
  company,
  setCompany,
  companyErr,
  setCompanyErr,
  designation,
  setDesignation,
  designationErr,
  setDesignationErr,
  working,
  setWorking,
  workingErr,
  setWorkingErr,
  isCurrentCompany,
  setIsCurrentCompany,
  jobProfile,
  setJobProfile,
  jobProfileErr,
  setJobProfileErr,
  workingSince,
  setWorkingSince,
  workingTill,
  setWorkingTill,
  workingSinceErr,
  setWorkingSinceErr,
  workingTillErr,
  setWorkingTillErr,
  workExperince,
  setWorkExperince,
  experienceList,
  setExperienceList,
  setCertificateFile,
  certificateFile,
  setcertificateFileError,
  certificateFileError,
  companyNameForCerti,
  setCompanyNameForCerti,
  setStartDate,
  startDate,
  setEndDate,
  endDate,
  certificationList,
  setCertificationList,
  setErrors,
  errors,
  errorShow,
  setErrorShow,
  setCertificateErrorShow,
  certificateerrorShow,
  setPreCertificate,
  preCertificate,
  preExperince,
  setPreExperince,
  name,
  phoneNumber,
  selectedDate,
  companyName,
  selectedGender,
  location,
  countryCode,
  flagCode,
  profileImage,
  referralCode,
  lastname,
  email,
  about,
  selectedTags,
  navigation,
  setUploadLicenseFile,
  uploadLicenseFile,
  formatedAddress,
  setAddData,
  addData,
  ActionSheetRef,
  ActionSheetRefIOS,
  uploadLicense,
  setUploadLicene,
}: any) {
  interface DropdownItem {
    title: string;
  }

  const IOS = Platform.OS === 'ios';
  const { selectPosition, userData } = useSelector((state: any) => state.auth); // Use your RootState type

  const [openBottomSheet, setOpenBottomSheet] = useState<boolean>(false);
  const [editExperince, setEditExperince] = useState<boolean>('');
  const [loader, setLoader] = useState<any>(false);
  const [loaderLicence, setLoaderLicence] = useState<any>(false);
  const dispatch = useDispatch();
  const [submitLoader, setSubmitLoader] = useState<boolean>(false);
  const [edit, setEdit] = useState<boolean>(false);
  const [editCertificate, setEditCertificate] = useState<boolean>(false);
  const [editCertificateList, setEditCertificateList] =
    useState<boolean>(false);
  const [checkSaveCerti, setCheckSaveCerti] = useState(false);
  const [checkSaveexperince, setCheckSaveExperince] = useState(false);
  // Update isUploadValid whenever cvFile or licenseFiles change
  useEffect(() => {
    setIsUploadValid(cvFile !== null && licenseFiles.length > 0);
  }, [cvFile, licenseFiles, setIsUploadValid]);
  const formattedDate = selectedDate ? formatDate(selectedDate) : ''; // Format the date
  const handleSubmit = async () => {
    setSubmitLoader(true);
    const newObj: NewObj = {
      firstName: name || undefined,
      lastName: lastname || undefined,
      email: email || undefined,
      phoneNumber: phoneNumber || undefined,
      birthDate: formattedDate || undefined,
      company: companyName || undefined,
      gender: selectedGender ? selectedGender.toLowerCase() : undefined,
      location: location?.description || '',
      countryCode: `+${countryCode}` || undefined,
      flagCode: flagCode,
      skillIds: !isEmpty(selectedTags) ? selectedTags : [],
      shortAddress: formatedAddress,
      about: userData?.about || '',
      coordinates: {
        lat: location?.lat || '',
        long: location?.long || '',
      },
    };

    newObj.cv = (() => {
      if (typeof cvFile === 'string') {
        if (cvFile.includes('http')) {
          // Extract the file name from the URL
          return cvFile.split('/').pop();
        } else {
          // Return the string directly if it doesn't contain 'http'
          return cvFile;
        }
      } else if (cvFile && typeof cvFile === 'object') {
        // Return the fileName if cvFile is an object
        return cvFile.fileName;
      }
      return ''; // Fallback if cvFile is neither string nor valid object
    })();
    newObj.profilePhoto =
      profileImage && profileImage?.includes('http')
        ? profileImage?.split('/').pop()
        : profileImage || '';
    // newObj.profilePhoto = 'file-1738313014744.jpg';
    // const d = profileImage?.split('/').length;
    const updatedLicenseFiles = licenseFiles
      .map((file: any) => {
        if (typeof file === 'string') {
          // If the file is a URL, extract the file name
          return file.split('/').pop();
        } else if (file?.fileName) {
          // If the file is an object, use the fileName property
          return file.fileName;
        }
        return null;
      })
      .filter(Boolean); // Filter out any null values

    // Update the licenseFiles state with the extracted file names

    try {
      const res = await getApiData({
        endpoint: BaseSetting.endpoints.updateUser,
        method: 'POST',
        data: newObj,
      });
      if (res?.status === true) {
        // Toast.show(res?.message || translate('err', ''), Toast.BOTTOM);
        setSubmitLoader(false);
        dispatch(authActions.setUserData(res?.data) as any);
        dispatch(authActions.setUserProfileData(res?.data) as any);
        onNext();
      } else {
        console.log('error', res);
        setSubmitLoader(false);
      }
    } catch (err) {
      console.log('check Error');
      setSubmitLoader(false);

      // Toast.show(translate('err', ''), Toast.BOTTOM);
    }
  };

  const closeActionSheet = () => {
    if (IOS) {
      ActionSheetRefIOS.current.close();
    } else {
      ActionSheetRef.current.hide();
    }
  };
  const handleCvFileSelect = async () => {
    try {
      const [file]: any = await DocumentPicker.pick({
        type: [
          DocumentPicker.types.pdf,
          DocumentPicker.types.doc,
          DocumentPicker.types.docx,
        ],
      });
      // Maximum allowed file size in bytes (10MB)
      if (file.size > maxFileSize) {
        setCvError('Please upload valid files under 10MB');
        closeActionSheet();
        return;
      }
      setLoader(true);
      // Pass the selected file and type to handleFilePick
      const fileRes = await handleFilePick(file, 'cv');
      if (!isEmpty(fileRes)) {
        setCvFile(fileRes?.data);
      }
      closeActionSheet();
      setCvError('');
      setLoader(false);
    } catch (err) {
      closeActionSheet();
      if (!DocumentPicker.isCancel(err)) {
        setCvError('Error selecting file');
      }
      setLoader(false);
    }
  };

  const handlePhotoSelect = async () => {
    try {
      const photo: any = await ImageCropPicker.openPicker({
        mediaType: 'photo',
        cropping: false, // Set to true if you want cropping
        smartAlbums: smartAlbums,
      });

      if (photo.size > maxFileSize) {
        setCvError('Please upload valid photos under 10MB');
        closeActionSheet();
        return;
      }

      setLoader(true);

      // Pass the selected photo to handleFilePick
      const fileRes = await handleFilePick(photo, 'image');
      if (!isEmpty(fileRes)) {
        setCvFile(fileRes?.data);

      }
      closeActionSheet();
      setCvError('');
      setLoader(false);
    } catch (err) {
      // setCvError('Error selecting photo');
      setLoader(false);
    }
  };

  // this is for open camara..

  const handleCertificateFileSelect = async () => {
    try {
      const [file]: any = await DocumentPicker.pick({
        type: [
          DocumentPicker.types.pdf,
          DocumentPicker.types.doc,
          DocumentPicker.types.docx,
          DocumentPicker.types.images,
        ],
      });
      // Maximum allowed file size in bytes (10MB)
      if (file.size > maxFileSize) {
        setcertificateFileError('Please upload valid files under 10MB');
        return;
      }

      setLoader(true);

      // Pass the selected file and type to handleFilePick
      const fileRes = await handleFilePick(file, 'cv');
      if (!isEmpty(fileRes)) {
        setCertificateFile(fileRes?.data);
      }
      setcertificateFileError('');
      setLoader(false);
    } catch (err) {
      if (!DocumentPicker.isCancel(err)) {
        setcertificateFileError('Error selecting file');
      }
      setLoader(false);
    }
  };

  // const handleLicenseFileSelect = async () => {
  //   try {
  //     const files: any = await DocumentPicker.pick({
  //       type: [
  //         DocumentPicker.types.images, // Covers jpg and png
  //         'application/vnd.openxmlformats-officedocument.wordprocessingml.document', // docx MIME type
  //       ],
  //       allowMultiSelection: editLicense ? false : true,
  //     });

  //     setLoaderLicence(true);

  //     // Filter valid files based on size
  //     const validFiles = files.filter(
  //       (file: any) => file.size <= 5 * 1024 * 1024, // Ensure size <= 5MB
  //     );

  //     if (validFiles.length === 0) {
  //       setLicenseError('Please upload valid files under 5MB');
  //       setLoaderLicence(false);
  //       return;
  //     }

  //     // Check if adding new files exceeds the limit
  //     const totalFiles = licenseFiles.length + validFiles.length;
  //     if (totalFiles > 6) {
  //       setLicenseError('You can upload a maximum of 6 files');
  //       setLoaderLicence(false);
  //       return validFiles; // Do not add any new files
  //     }

  //     // Upload files within the limit
  //     validFiles.forEach(async (file: any) => {
  //       setLicenseError('');
  //       const fileRes = await handleFilePick(file, 'license');
  //       if (fileRes?.status) {
  //         setLicenseFiles((prevFiles: any) => [
  //           ...prevFiles,
  //           {
  //             fileName: fileRes?.data?.fileName,
  //             filePath: fileRes?.data?.filePath,
  //             fileSize: fileRes?.data?.fileSize,
  //           },
  //         ]);

  //         setLoaderLicence(false);
  //       } else {
  //         setLoaderLicence(false);
  //       }
  //     });
  //   } catch (err) {
  //     if (!DocumentPicker.isCancel(err)) {
  //       setLicenseError('Error selecting files');
  //     }
  //     setLoaderLicence(false);
  //   }
  // };

  const handleLicenseFileSelect = async () => {
    try {
      const files: any = await DocumentPicker.pick({
        type: [
          DocumentPicker.types.pdf,
          DocumentPicker.types.doc,
          DocumentPicker.types.docx,
        ],
        allowMultiSelection: false,
      });

      setLoaderLicence(true);

      // Check if adding new files exceeds the limit
      if (uploadLicense.length >= 5) {
        setLicenseError('You can upload a maxismum of 5 files');
        setLoaderLicence(false);
        return;
      }

      // Filter valid files based on size
      const validFiles = files.filter(
        (file: any) => file.size <= maxFileSize,
      );

      if (validFiles.length === 0) {
        setLicenseError('Please upload valid files under 10MB');
        setLoaderLicence(false);
        closeActionSheet();
        return;
      }

      validFiles.forEach(async (file: any) => {
        setLicenseError('');
        const fileRes = await handleFilePick(file, 'license');
        if (fileRes?.status) {
          setUploadLicenseFile((prevFiles: any) => {
            if (prevFiles.length >= 5) {
              setLicenseError('You can upload ass maximum of 5 files');
              return prevFiles;
            }
            return [
              ...prevFiles,
              {
                fileName: fileRes?.data?.fileName,
                filePath: fileRes?.data?.filePath,
                fileSize: fileRes?.data?.fileSize,
              },
            ];
          });
          setUploadLicene((prevFiles: any) => {
            if (prevFiles.length >= 5) {
              setLicenseError('You can upload a maximssum of 5 files');
              return prevFiles;
            }
            return [
              ...prevFiles,
              {
                fileName: fileRes?.data?.fileName,
                filePath: fileRes?.data?.filePath,
                fileSize: fileRes?.data?.fileSize,
              },
            ];
          });
        }
      });
      closeActionSheet();
    } catch (err) {
      if (!DocumentPicker.isCancel(err)) {
        setLicenseError('Error selecting files');
      }
    } finally {
      setLoaderLicence(false);
    }
  };

  const handleImageFileSelect = async () => {
    try {
      const file: any = await ImageCropPicker.openPicker({
        mediaType: 'photo',
        cropping: false, // Set to true if you want cropping
        smartAlbums: smartAlbums,
      });

      setLoaderLicence(true);

      // Check if adding a new file exceeds the limit
      if (uploadLicense.length >= 5) {
        setLicenseError('You can upload a maximum of 5 images');
        setLoaderLicence(false);
        return;
      }

      // Validate file size (under 5MB)
      if (file.size > maxFileSize) {
        setLicenseError('Please upload a valid image under 10MB');
        setLoaderLicence(false);
        closeActionSheet();
        return;
      }

      setLicenseError('');
      const fileRes = await handleFilePick(file, 'license');
      if (fileRes?.status) {
        setUploadLicenseFile((prevFiles: any) => {
          if (prevFiles.length >= 5) {
            setLicenseError('You can upload a maximum of 5 images');
            return prevFiles;
          }
          return [
            ...prevFiles,
            {
              fileName: fileRes?.data?.fileName,
              filePath: fileRes?.data?.filePath,
              fileSize: fileRes?.data?.fileSize,
            },
          ];
        });

        setUploadLicene((prevFiles: any) => {
          if (prevFiles.length >= 5) {
            setLicenseError('You can upload a maximum of 5 images');
            return prevFiles;
          }
          return [
            ...prevFiles,
            {
              fileName: fileRes?.data?.fileName,
              filePath: fileRes?.data?.filePath,
              fileSize: fileRes?.data?.fileSize,
            },
          ];
        });
      }

      closeActionSheet();
    } catch (err) {
      // setLicenseError('Error selecting image');
    } finally {
      setLoaderLicence(false);
    }
  };

  const handleOpenCamera = async () => {
    try {
      if (uploadLicense.length >= 5) {
        setLicenseError('You can upload a maximsssum of 5 files');
        return;
      }

      const image = await ImagePicker.openCamera({
        width: 300,
        height: 400,
        cropping: true,
        mediaType: 'photo',
      });

      setLoaderLicence(true);

      if (image.size > maxFileSize) {
        setLicenseError('Please upload an image under 10MB');
        setLoaderLicence(false);
        return;
      }

      setLicenseError('');
      const fileRes = await handleFilePick(image, 'license');

      if (fileRes?.status) {
        setUploadLicenseFile((prevFiles: any) => {
          if (prevFiles.length >= 5) {
            setLicenseError('You can upload a maximum of 5 files');
            return prevFiles;
          }
          return [
            ...prevFiles,
            {
              fileName: fileRes?.data?.fileName,
              filePath: fileRes?.data?.filePath,
              fileSize: fileRes?.data?.fileSize,
            },
          ];
        });
        setUploadLicene((prevFiles: any) => {
          if (prevFiles.length >= 5) {
            setLicenseError('You can upload a maximum of 5 files');
            return prevFiles;
          }
          return [
            ...prevFiles,
            {
              fileName: fileRes?.data?.fileName,
              filePath: fileRes?.data?.filePath,
              fileSize: fileRes?.data?.fileSize,
            },
          ];
        });
      }
      closeActionSheet();
    } catch (err) {
      if (err.message !== 'User cancelled image selection') {
        setLicenseError('Error capturing image');
      }
    } finally {
      setLoaderLicence(false);
    }
  };

  const handleNextPress = () => {
    handleSubmit();
  };

  const refRBSheet = useRef<any>(null);

  return (
    <View style={styles.container}>
      <KeyboardAwareScrollView
        bounces={false}
        contentContainerStyle={styles.scrollContainer}
        showsVerticalScrollIndicator={false}
        keyboardShouldPersistTaps="handled"
        enableOnAndroid={false}>
        <AnimatedView>
          <View style={styles.fileContainer}>
            <Text style={styles.resumeText}>
              {translate('uploadYourDocument', '')}
            </Text>
            <Text style={[styles.desTxt, { marginTop: 5 }]}>
              {translate('dataDexcription', '')}
            </Text>
            <Text
              style={[
                styles.desTxt,
                { marginVertical: 10, fontStyle: 'italic' },
              ]}>
              {translate('documentsReveiew', '')}
            </Text>
            <View style={styles.chooseFileContainer}>
              <TouchableOpacity
                activeOpacity={0.8}
                onPress={() => {
                  // refRBSheet.current?.open();
                }}>
                <View style={[styles.dropdownContainer, { marginBottom: 0 }]}>
                  <View style={styles.btnView}>
                    <Button
                      onPress={() => {
                        setAddData('CV/Resume');
                        setEdit(false);
                        setEditExperince('');
                        setEditCertificate(false);
                        setEditCertificateList('');
                        setUploadLicenseFile('');
                        setOpenBottomSheet(true);
                        setCheckSaveCerti(false);
                        setCheckSaveExperince(false);
                        refRBSheet.current?.open();
                      }}
                      txtStyles={{ fontSize: 14 }}
                      type="outlined">
                      {translate('cvResume', '')}
                    </Button>
                  </View>
                  <View style={styles.btnView}>
                    <Button
                      onPress={() => {
                        setAddData('Upload Certifications');
                        setEdit(false);
                        setEditExperince('');
                        setEditCertificate(false);
                        setEditCertificateList('');
                        setUploadLicenseFile('');
                        setOpenBottomSheet(true);
                        setCheckSaveCerti(false);
                        setCheckSaveExperince(false);
                        refRBSheet.current?.open();
                      }}
                      txtStyles={{ fontSize: 14 }}
                      type="outlined">
                      {translate('UploadCertification', '')}
                    </Button>
                  </View>
                  <View style={styles.btnView}>
                    <Button
                      onPress={() => {
                        setAddData('Upload License');
                        setEdit(false);
                        setEditExperince('');
                        setEditCertificate(false);
                        setEditCertificateList('');
                        setUploadLicene(licenseFiles);
                        setUploadLicenseFile('');
                        setOpenBottomSheet(true);
                        setCheckSaveCerti(false);
                        setCheckSaveExperince(false);
                        refRBSheet.current?.open();
                      }}
                      txtStyles={{ fontSize: 14 }}
                      type="outlined">
                      {translate('UploadLiscense', '')}
                    </Button>
                  </View>
                  <View>
                    <Button
                      onPress={() => {
                        setAddData('Enter Experience');
                        setEdit(false);
                        setEditExperince('');
                        setEditCertificate(false);
                        setEditCertificateList('');
                        setUploadLicenseFile('');
                        setOpenBottomSheet(true);
                        setCheckSaveCerti(false);
                        setCheckSaveExperince(false);
                        refRBSheet.current?.open();
                      }}
                      txtStyles={{ fontSize: 14 }}
                      borderStyle={{ borderColor: 'transparent' }}
                      type="outlined">
                      Enter Experience
                    </Button>
                  </View>
                </View>
              </TouchableOpacity>
            </View>
          </View>
          <View style={{ marginVertical: 10 }}>
            <CvFileComponent
              cvFile={cvFile}
              deleteFile={deleteFile}
              loader={loader}
              navigation={navigation}
            />
          </View>
          {/* <View style={styles.fileContainer}>
            {cvFile ? (
              <>
                <View>
                  <Text style={styles?.resumeText}>
                    {translate('Resume', '')}
                  </Text>
                </View>
                <View style={styles.cvFileContainer}>
                  <View style={styles.fileInfoContainer}>
                    <View style={styles.iconSty}>
                      <CustomIcon
                        name="document"
                        size={25}
                        color={styles.fileIconColor.color}
                      />
                    </View>

                    <View style={styles.fileDetailsContainer}>
                      <Text numberOfLines={1} style={styles.fileNameText}>
                        {typeof cvFile === 'string' && cvFile.includes('http')
                          ? cvFile.split('/').pop()
                          : cvFile?.fileName || '-'}
                      </Text>

                      {cvFile?.fileSize && (
                        <Text style={styles.fileSizeText}>
                          Size: {cvFile.fileSize}
                        </Text>
                      )}
                    </View>
                  </View>

                  <TouchableOpacity
                    onPress={() => {
                      if (Review !== 'reviewType') deleteFile('cv');
                    }}
                    style={styles.removeFileButton}>
                    {loader === 'cv' ? (
                      <ActivityIndicator color={BaseColors.primary} />
                    ) : (
                      <EIcon
                        name="cross"
                        size={20}
                        color={BaseColors.textGrey}
                      />
                    )}
                  </TouchableOpacity>
                </View>
              </>
            ) : null}
          </View> */}
          {/* {!isEmpty(licenseFiles) && (
            <View style={styles.fileContainer}>
              <Text style={styles.resumeText}>{translate('license', '')}</Text>
              {licenseFiles?.map((file, index) => (
                <View key={index} style={styles.licenseFileContainer}>
                  <View style={styles.fileInfoContainer}>
                    <View style={styles.iconSty}>
                      <CustomIcon
                        name="document"
                        size={25}
                        color={styles.fileIconColor.color}
                      />
                    </View>
                    <View style={styles.fileDetailsContainer}>
                      <Text numberOfLines={1} style={styles.fileNameText}>
                        {file
                          ? typeof file === 'string' && file.includes('http')
                            ? file.split('/').pop()
                            : typeof file === 'string'
                            ? file
                            : file?.fileName || '-'
                          : '-'}
                      </Text>
                      {file?.fileSize && (
                        <Text style={styles.fileSizeText}>
                          {translate('Size', '')}: {file?.fileSize || '-'}
                        </Text>
                      )}
                    </View>
                  </View>

                  <TouchableOpacity
                    style={styles.removeFileButton}
                    onPress={() =>
                      Review === 'reviewType'
                        ? null
                        : deleteFile('licence', index)
                    }>
                    {loader === index ? (
                      <ActivityIndicator color={BaseColors.primary} />
                    ) : (
                      <EIcon
                        name="cross"
                        size={20}
                        color={BaseColors.textGrey}
                      />
                    )}
                  </TouchableOpacity>
                </View>
              ))}
            </View>
          )} */}

          <LicenseFileList
            licenseFiles={licenseFiles}
            deleteFile={deleteFile}
            loader={loader}
            Review={Review}
            navigation={navigation}
            setLicenseFiles={setLicenseFiles}
            setUploadLicenseFile={setUploadLicenseFile}
            uploadLicenseFile={uploadLicenseFile}
            setUploadLicene={setUploadLicene}
          />
          <View style={{ marginVertical: 10 }}>
            <ExperienceCard
              experienceList={experienceList}
              setExperienceList={setExperienceList}
              preExperince={preExperince}
              setPreExperince={setPreExperince}
              setOpenBottomSheet={setOpenBottomSheet}
              refRBSheet={refRBSheet}
              edit={edit}
              setEdit={setEdit}
              setEditExperince={setEditExperince}
              editExperince={editExperince}
              setEditCertificate={setEditCertificate}
              editCertificate={editCertificate}
              setCheckSaveExperince={setCheckSaveExperince}
              setCheckSaveCerti={setCheckSaveCerti}
              setAddData={setAddData}
            />
          </View>

          <CertificateCard
            certificationList={certificationList}
            setCertificationList={setCertificationList}
            setPreCertificate={setPreCertificate}
            preCertificate={preCertificate}
            setEditCertificate={setEditCertificate}
            editCertificate={editCertificate}
            refRBSheet={refRBSheet}
            setEditCertificateList={setEditCertificateList}
            editCertificateList={editCertificateList}
            setEdit={setEdit}
            navigation={navigation}
            setCheckSaveExperince={setCheckSaveExperince}
            setCheckSaveCerti={setCheckSaveCerti}
            setAddData={setAddData}
          />
        </AnimatedView>
        <UploadData
          refRBSheet={refRBSheet}
          setOpenBottomSheet={setOpenBottomSheet}
          handleCvFileSelect={handleCvFileSelect}
          deleteFile={deleteFile}
          cvFile={cvFile}
          cvError={cvError}
          loader={loader}
          setLoader={setLoader}
          Review={Review}
          licenseFiles={licenseFiles}
          licenseError={licenseError}
          loaderLicence={loaderLicence}
          handleLicenseFileSelect={handleLicenseFileSelect}
          // Passing all state fields
          company={company}
          setCompany={setCompany}
          companyErr={companyErr}
          setCompanyErr={setCompanyErr}
          designation={designation}
          setDesignation={setDesignation}
          designationErr={designationErr}
          setDesignationErr={setDesignationErr}
          working={working}
          setWorking={setWorking}
          workingErr={workingErr}
          setWorkingErr={setWorkingErr}
          isCurrentCompany={isCurrentCompany}
          setIsCurrentCompany={setIsCurrentCompany}
          jobProfile={jobProfile}
          setJobProfile={setJobProfile}
          jobProfileErr={jobProfileErr}
          setJobProfileErr={setJobProfileErr}
          workingSince={workingSince}
          setWorkingSince={setWorkingSince}
          workingTill={workingTill}
          setWorkingTill={setWorkingTill}
          workingSinceErr={workingSinceErr}
          setWorkingSinceErr={setWorkingSinceErr}
          workingTillErr={workingTillErr}
          setWorkingTillErr={setWorkingTillErr}
          setWorkExperince={setWorkExperince}
          workExperince={workExperince}
          setExperienceList={setExperienceList}
          experienceList={experienceList}
          setCertificateFile={setCertificateFile}
          certificateFile={certificateFile}
          setcertificateFileError={setcertificateFileError}
          certificateFileError={certificateFileError}
          handleCertificateFileSelect={handleCertificateFileSelect}
          setCompanyNameForCerti={setCompanyNameForCerti}
          companyNameForCerti={companyNameForCerti}
          setStartDate={setStartDate}
          startDate={startDate}
          setEndDate={setEndDate}
          endDate={endDate}
          certificationList={certificationList}
          setCertificationList={setCertificationList}
          setErrors={setErrors}
          errors={errors}
          setErrorShow={setErrorShow}
          errorShow={errorShow}
          setCertificateErrorShow={setCertificateErrorShow}
          certificateerrorShow={certificateerrorShow}
          setPreCertificate={setPreCertificate}
          preCertificate={preCertificate}
          setPreExperince={setPreExperince}
          preExperince={preExperince}
          setEdit={setEdit}
          edit={edit}
          setEditExperince={setEditExperince}
          editExperince={editExperince}
          setEditCertificate={setEditCertificate}
          editCertificate={editCertificate}
          setEditCertificateList={setEditCertificateList}
          editCertificateList={editCertificateList}
          uploadLicenseFile={uploadLicenseFile}
          setUploadLicenseFile={setUploadLicenseFile}
          setLicenseFiles={setLicenseFiles}
          addData={addData}
          setAddData={setAddData}
          setCheckSaveExperince={setCheckSaveExperince}
          setCheckSaveCerti={setCheckSaveCerti}
          checkSaveexperince={checkSaveexperince}
          checkSaveCerti={checkSaveCerti}
          setCvFile={setCvFile}
          ActionSheetRef={ActionSheetRef}
          ActionSheetRefIOS={ActionSheetRefIOS}
          handleOpenCamera={handleOpenCamera}
          uploadLicense={uploadLicense}
          setUploadLicene={setUploadLicene}
          handlePhotoSelect={handlePhotoSelect}
          handleImageFileSelect={handleImageFileSelect}
        />
      </KeyboardAwareScrollView>

      {Review === 'reviewType' ? null : (
        <View style={styles.nextButtonContainer}>
          <Button type="text" onPress={handleNextPress} loading={submitLoader}>
            {translate('next', '')}
          </Button>
        </View>
      )}
    </View>
  );
}
