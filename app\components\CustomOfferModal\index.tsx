import React, { useCallback, useEffect, useState } from 'react';
import {
  Dimensions,
  Keyboard,
  Modal,
  ScrollView,
  Text,
  TouchableWithoutFeedback,
  View,
} from 'react-native';
import Button from '../../components/UI/Button';
import styles from './styles';
import { translate } from '@language/Translate';
import TextInput from '@components/UI/TextInput';
import {
  calculateDuration,
  formatDate,
  getDuration,
  getDurationLabel,
  isIOS,
} from '@app/utils/CommonFunction';
import { BaseColors } from '@config/theme';
import {
  camelCase,
  isEmpty,
  isObject,
  startCase,
  trim,
} from '@app/utils/lodashFactions';
import moment from 'moment';
import CounterOfferCard from '@components/SeekerCard/CounterOfferCard';
import DropdownList from '@components/DropDownList';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import SwitchComponent from '@components/SwitchComponant';
import { getApiData } from '@app/utils/apiHelper';
// import Toast from 'react-native-simple-toast';
import BaseSetting from '@config/setting';
import PaymentBreakdown from '@components/PaymentBreakdown';
import { useAppSelector } from '@components/UseRedux';
import FIcon from 'react-native-vector-icons/FontAwesome6'; // Replace with your specific icon library if different
import { minPrice } from '@screens/Settings/staticData';

const { height } = Dimensions.get('window');

interface CustomOfferModalInterface {
  setState?: any;
  state?: any;
  title?: string;
  onSubmit?: any;
  onCancel?: any;
  jobDetail?: any;
  navigation?: any;
  type?: string;
}

const stData = {
  // Date
  startDate: undefined,
  endDate: undefined,
  sDErr: false,
  eDErr: false,
  sDErrTxt: undefined,
  eDErrTxt: undefined,

  // Time
  startTime: undefined,
  endTime: undefined,
  sTErr: false,
  eTErr: false,
  sTErrTxt: undefined,
  eTErrTxt: undefined,

  msg: undefined,
  msgErr: false,
  msgErrTxt: '',
  salaryAmount: '',
  salaryAmountErr: false,
  salaryAmountErrTxt: '',

  flatRate: true,
  durationErr: false,
  durationErrTxt: '',
  totalSalary: '',
  durationCal: '',

  serviceCharge: '',
  totalSalaryAmount: '',
  totalEstimateCharge: '',
};

const durationOptions: any = [
  // { title: translate('Flat Rate') },
  { title: translate('Per hour', '') },
  { title: translate('Per day', '') },
  { title: translate('Per week', '') },
  { title: translate('Per month', '') },
  { title: translate('Per year', '') },
];

export default React.memo(function CustomOfferModal({
  setState,
  state,
  title,
  onSubmit,
  onCancel,
  jobDetail,
  navigation,
  type,
}: CustomOfferModalInterface) {
  const { userData } = useAppSelector((state: any) => state.auth); // Use your RootState type
  const applicant = state?.applicant || {};
  // Only for counter offer job approve payment
  const jobD = applicant?.startDate ? applicant || jobDetail || {} : {};
  const isEmployer = userData?.id === jobDetail?.userId;
  const isApproveType = state?.type === 'Approved';
  const isDeclinedType = state?.type === 'Declined';
  const isCounterType = state?.type === 'counter' || state?.type === 'edit';
  const isEdit = state?.type === 'edit';

  const [cState, setCState] = useState({
    ...stData,
    startDate: isEdit ? moment(applicant?.startDate, 'MM/DD/YYYY') : undefined,
    endDate: isEdit ? moment(applicant?.endDate, 'MM/DD/YYYY') : undefined,
    startTime: isEdit
      ? moment(applicant?.startTime, 'hh:mm A').toDate()
      : undefined,
    endTime: isEdit
      ? moment(applicant?.endTime, 'hh:mm A').toDate()
      : undefined,
    // salaryAmount: isEdit
    //   ? applicant?.salaryAmount || applicant?.finalPrice || ''
    //   : '',
    msg: isEdit ? applicant?.message : undefined,
    duration: isEdit ? getDurationLabel(applicant?.duration) : 'Flat Rate',
  });

  const {
    startDate,
    endDate,
    sDErr,
    eDErr,
    sDErrTxt,
    eDErrTxt,
    startTime,
    endTime,
    sTErr,
    sTErrTxt,
    eTErr,
    msg,
    // duration,
    msgErr,
    msgErrTxt,
    salaryAmountErr,
    salaryAmountErrTxt,
    eTErrTxt,
    // durationErr,
    durationErrTxt,
    flatRate,
    // totalSalary,
    durationCal,
  } = cState;

  const formattedStartDatecheck: any = startDate;
  const minDate = new Date(formattedStartDatecheck); // Convert to Date object

  const [salaryAmount, setSalaryAmount] = useState('');

  const [duration, setDuration] = useState<string>(
    getDuration(applicant?.duration),
  );
  const [isKeyboardVisible, setKeyboardVisible] = useState(false);

  const [durationErr, setDurationErr] = useState({
    err: false,
    txt: '',
  });

  useEffect(() => {
    const keyboardDidShowListener = Keyboard.addListener(
      'keyboardDidShow',
      () => setKeyboardVisible(true),
    );
    const keyboardDidHideListener = Keyboard.addListener(
      'keyboardDidHide',
      () => setKeyboardVisible(false),
    );

    return () => {
      keyboardDidShowListener.remove();
      keyboardDidHideListener.remove();
    };
  }, []);

  const [totalSalary, setTotalSalary] = useState<string>();
  console.log('totalSalary ===>', totalSalary);

  const formatText = text => {
    return String(text)
      .replace(/([A-Z])/g, ' $1') // Add space before uppercase letters
      .toLowerCase() // Convert everything to lowercase
      .replace(/^./, str => str.toUpperCase()); // Capitalize first letter
  };

  const getType = (st: any, et: any) => {
    const start = moment(st, 'MM/DD/YYYY');
    const end = moment(et, 'MM/DD/YYYY');
    // Calculate total days including the start and end dates
    const totalDays = end.diff(start, 'days') + 1; // Add 1 to include the end date
    // Determine the duration type dynamically using a single conditional structur

    const durType =
      totalDays >= 365
        ? 'Per year'
        : totalDays >= 28
          ? 'Per month'
          : totalDays >= 7
            ? 'Per week'
            : totalDays >= 2
              ? 'Per day'
              : 'Per hour';

    console.log('totalDays ==>', durType, totalDays, st, et);
    return durType;
  };

  interface ApplicantInterface {
    startDate?: string;
    endDate?: string;
    startTime?: string;
    endTime?: string;
    duration?: string;
    finalPrice?: number | string;
    salaryAmount?: number | string;
    isEmployerFilled: boolean | undefined;
    message?: string;
    // Add any other properties that the applicant object may have
  }


  const checkSalary = (isValid?: boolean) => {
    if (isValid === false) {
      setCState(prevState => ({
        ...prevState,
        salaryAmountErr: true,
        salaryAmountErrTxt: translate('rate50Required', ''),
      }));
    } else {
      setCState(prevState => ({
        ...prevState,
        salaryAmountErr: false,
        salaryAmountErrTxt: '',
      }));
    }
  };
  // App Side Calculation code
  const calculateDur = useCallback(
    (
      d: string,
      durType: string,
      applicantDetail?: ApplicantInterface,
      type?: string,
    ) => {
      const isBlankProps = !d && !durType && isEmpty(applicantDetail) && !type;
      const isEditedData =
        isEdit && isObject(applicantDetail) && !isEmpty(applicantDetail);
      let durationType = '';
      if (isEditedData) {
        durationType = getType(
          applicantDetail?.startDate,
          applicantDetail?.endDate,
        );
      }
      if (
        (!durType && !duration && !isEditedData) ||
        isBlankProps ||
        applicantDetail?.isEmployerFilled
      ) {
        // durationType = getType(startDate, d || endDate);
        durationType = getType(
          moment(
            applicantDetail?.isEmployerFilled
              ? applicantDetail?.startDate
              : startDate,
          ).format('MM/DD/YYYY'),
          moment(
            applicantDetail?.isEmployerFilled
              ? applicantDetail?.endDate
              : d || endDate,
          ).format('MM/DD/YYYY'),
        );
      }

      // console.log('durType ==>', isEditedData, d, durationType, durType, moment(d).format());

      const dur = calculateDuration(
        isEditedData || applicantDetail?.isEmployerFilled
          ? moment(applicantDetail?.startDate, 'MM/DD/YYYY')
          : startDate,
        isEditedData || applicantDetail?.isEmployerFilled
          ? moment(applicantDetail?.endDate, 'MM/DD/YYYY')
          : durType !== 'startTime' &&
            durType !== 'endTime' &&
            type !== 'salaryAmount' &&
            durType
            ? d
            : endDate,
        isEditedData || applicantDetail?.isEmployerFilled
          ? moment(applicantDetail?.startTime, 'hh:mm A')
          : durType === 'startTime'
            ? d
            : startTime,
        isEditedData || applicantDetail?.isEmployerFilled
          ? moment(applicantDetail?.endTime, 'hh:mm A')
          : durType === 'endTime'
            ? d
            : endTime,
        isEditedData || isBlankProps || applicantDetail?.isEmployerFilled
          ? durationType
          : durType !== 'startTime' && durType !== 'endTime' && durType
            ? durType
            : duration,
      );

      console.log(
        'calculateDuration dur ===>',
        durationType,
        getType(
          moment(startDate).format('MM/DD/YYYY'),
          moment(d || endDate).format('MM/DD/YYYY'),
        ),
        isEditedData,
        dur,
        d,
        endTime,
        {
          startDate: isEditedData
            ? moment(applicantDetail?.startDate, 'MM/DD/YYYY')
            : startDate,
          endDate: isEditedData
            ? moment(applicantDetail?.endDate, 'MM/DD/YYYY')
            : durType !== 'startTime' &&
              durType !== 'endTime' &&
              type !== 'salaryAmount' &&
              durType !== ''
              ? d
              : endDate,
          startTime: isEditedData
            ? moment(applicantDetail?.startTime, 'hh:mm A').format('hh:mm A')
            : durType === 'startTime'
              ? d
              : startTime,
          endTime: isEditedData
            ? moment(applicantDetail?.endTime, 'hh:mm A').format('hh:mm A')
            : durType === 'endTime'
              ? d
              : endTime,
          durationType:
            isEditedData || isBlankProps
              ? durationType
              : durType !== 'startTime' && durType !== 'endTime' && durType
                ? durType
                : duration,
          end: isEditedData
            ? moment(applicantDetail?.endDate, 'MM/DD/YYYY')
            : durType !== 'startTime' &&
              durType !== 'endTime' &&
              type !== 'salaryAmount' &&
              !durType
              ? d
              : endDate,
        },
        startTime,
        endTime,
        // durType !== 'startTime' && durType !== 'endTime' && durType ? durType : duration,
        // duration || null,
        // startDate,
        // endDate
      );
      if (dur === 'Invalid type') {
        setCState((p: any) => ({ ...p, durationCal: '' }));
      } else {
        if (isBlankProps || applicantDetail?.isEmployerFilled) {
          setDuration(durationType);
        }
        setCState((p: any) => ({ ...p, durationCal: dur }));
      }
    },
    [duration, startDate, endDate, startTime, endTime, applicant, isEdit],
  );
  const checkAvailable = useCallback(
    async (
      val?: any,
      type = '',
      applicantDetail?: ApplicantInterface,
      isFlatRate?: boolean,
      durType?: string,
      flatRateVal?: boolean,
    ) => {
      const isEditedData =
        isEdit && isObject(applicantDetail) && !isEmpty(applicantDetail);
      const data = {
        sd:
          isEditedData || applicantDetail?.isEmployerFilled
            ? moment(applicantDetail?.startDate, 'MM/DD/YYYY')
            : startDate,
        ed:
          isEditedData || applicantDetail?.isEmployerFilled
            ? moment(applicantDetail?.endDate, 'MM/DD/YYYY')
            : endDate,
        st:
          isEditedData || applicantDetail?.isEmployerFilled
            ? applicantDetail?.isEmployerFilled
              ? moment(applicantDetail?.startTime).format('hh:mm A')
              : applicantDetail?.startTime
            : startTime,
        et:
          isEditedData || applicantDetail?.isEmployerFilled
            ? applicantDetail?.isEmployerFilled
              ? moment(applicantDetail?.endTime).format('hh:mm A')
              : applicantDetail?.endTime
            : endTime,
        salaryAmount:
          isEditedData || applicantDetail?.isEmployerFilled
            ? applicantDetail?.salaryAmount || applicantDetail?.finalPrice
            : salaryAmount,
      };
      let d: any = {
        startDate: formatDate(type === 'startDate' ? val : data?.sd),
        endDate: formatDate(type === 'endDate' ? val : data?.ed),
        startTime: isEditedData
          ? data?.st
          : val || data?.st
            ? moment(type === 'startTime' ? val : data?.st).format('hh:mm A')
            : null,
        endTime: isEditedData
          ? data?.et
          : val || data?.et
            ? moment(type === 'endTime' ? val : data?.et).format('hh:mm A')
            : null,
        salaryAmount:
          type === 'salaryAmount'
            ? String(val).replace(/\$/g, '')
            : data?.salaryAmount || applicantDetail?.finalPrice,
      };

      // Extract the field that is changing and exclude it from the check
      const { [type]: ignoredField, ...fieldsToCheck } = d;

      // Check if all remaining fields (except the changed one) are empty
      const isMissingFields = Object.values(fieldsToCheck).some(
        value => !value,
      );

      console.log('d =>', isEditedData, applicantDetail, d);
      if (isMissingFields) {
        // setAmountLoader(false);
        // Toast.show('Please fill in all required fields', Toast.BOTTOM);
        return;
      }
      const isFlatValue = flatRateVal === true || flatRateVal === false;
      d.durationType =
        isEditedData || applicantDetail?.isEmployerFilled
          ? applicantDetail?.duration || getType(d?.startDate, d?.endDate)
          : flatRate && jobDetail?.isFlatRate && !isFlatValue
            ? undefined
            : camelCase(
              trim(
                type === 'durationType'
                  ? val
                  : type === 'endDate' && durType
                    ? formatText(durType)
                    : duration ||
                    formatText(
                      applicantDetail?.duration ||
                        getType(d?.startDate, d?.endDate),
                    ),
              ),
            );
      if (
        isEditedData
          ? !applicantDetail?.duration && isFlatRate
          : isFlatValue
            ? flatRateVal
            : flatRate && jobDetail?.isFlatRate
      ) {
        d.isFlatRate = isFlatValue ? flatRateVal : true;
        delete d.durationType;
      }
      // setAmountLoader(true);

      const url = BaseSetting.endpoints.generateSalary;
      console.log(
        'generateSalary =>',
        d,
        applicantDetail?.duration,
        camelCase(
          trim(
            type === 'durationType'
              ? val
              : duration || formatText(applicantDetail?.duration),
          ),
        ),
        getType(data?.sd, data?.ed),
        applicantDetail?.duration || getType(d?.startDate, d?.endDate),
        isEditedData || applicantDetail?.isEmployerFilled,
        applicantDetail?.isEmployerFilled,
        flatRate && jobDetail?.isFlatRate && !isFlatValue,
      );

      const resp = await getApiData({
        endpoint: url,
        method: 'POST',
        data: d,
      });

      console.log('respio ===>', resp);
      if (resp?.status) {
        // setServiceCharge(resp?.data?.serviceCharge);
        setTotalSalary(resp?.data?.totalSalaryAmount);
        // setTotalWithServiceCharge(resp?.data?.totalEstimateCharge);
        // setAmountLoader(false);
      } else {
        // Toast.show(resp?.message, Toast.BOTTOM);
      }
    },
    [
      startDate,
      endDate,
      startTime,
      endTime,
      salaryAmount,
      duration,
      flatRate,
      jobDetail,
      isEdit,
    ],
  );

  const getStateUpdate = useCallback(
    (type: string) => {
      if (type === 'employerData') {
        setCState({
          ...stData,
          startDate: jobD?.startDate
            ? moment(jobD?.startDate, 'MM/DD/YYYY')
            : undefined,
          endDate: jobD?.endDate
            ? moment(jobD?.endDate, 'MM/DD/YYYY')
            : undefined,
          startTime: jobD?.startDateTime
            ? moment(
              moment(jobD?.startDateTime).format('hh:mm A'),
              'hh:mm A',
            ).toDate()
            : undefined,
          endTime: jobD?.endDateTime
            ? moment(
              moment(jobD?.endDateTime).format('hh:mm A'),
              'hh:mm A',
            ).toDate()
            : undefined,
          salaryAmount: state?.salaryAmount
            ? state?.salaryAmount
            : jobD?.salaryAmount || jobD?.finalPrice
              ? jobD?.salaryAmount || jobD?.finalPrice || ''
              : '',
          duration: jobD?.duration
            ? getDurationLabel(jobD?.duration) || 'Flat Rate'
            : 'Flat Rate',
          flatRate: !jobD?.isFlatRate && jobD?.duration ? false : true,
        });
        if (jobD?.salaryAmount) {
          setSalaryAmount(jobD?.salaryAmount || '');
        }
        if (!jobD?.isFlatRate) {
          const data = {
            ...jobD,
            endDate: moment(jobD?.endDate, 'MM/DD/YYYY'),
            startDate: moment(jobD?.startDate, 'MM/DD/YYYY'),
            isEmployerFilled: true,
          };
          checkAvailable('', '', data, !jobD?.isFlatRate);
          calculateDur('', '', data);
        }
      } else if (isEdit && applicant) {
        setCState({
          ...stData,
          startDate: isEdit
            ? moment(applicant?.startDate, 'MM/DD/YYYY')
            : undefined,
          endDate: isEdit
            ? moment(applicant?.endDate, 'MM/DD/YYYY')
            : undefined,
          startTime: isEdit
            ? moment(
              moment(applicant?.startDateTime).format('hh:mm A'),
              'hh:mm A',
            ).toDate()
            : undefined,
          endTime: isEdit
            ? moment(
              moment(applicant?.endDateTime).format('hh:mm A'),
              'hh:mm A',
            ).toDate()
            : undefined,
          salaryAmount: isEdit
            ? applicant?.salaryAmount || applicant?.finalPrice || ''
            : '',
          msg: isEdit ? applicant?.message : undefined,
          duration: isEdit
            ? getDurationLabel(applicant?.duration) || 'Flat Rate'
            : 'Flat Rate',
          flatRate:
            !jobDetail?.isFlatRate || applicant?.duration ? false : true,
        });
        setSalaryAmount(applicant?.salaryAmount || applicant?.finalPrice || '');
        setDuration(
          applicant?.duration
            ? applicant.duration === 'perDay'
              ? 'Per day'
              : applicant.duration === 'perMonth'
                ? 'Per month'
                : applicant.duration === 'perHour'
                  ? 'Per hour'
                  : applicant.duration === 'perWeek'
                    ? 'Per week'
                    : applicant.duration === 'perYear'
                      ? 'Per year'
                      : ''
            : '',
        );
        setTimeout(() => {
          checkAvailable('', '', applicant, !jobDetail?.isFlatRate);
          calculateDur('', '', applicant);
        }, 500);
      }
    },
    [applicant, jobDetail, isEdit],
  );

  useEffect(() => {
    if (state?.customOfferModal || state?.counterOfferModal) {
      if (isEdit && applicant) {
        getStateUpdate('isEdit');
      }
    }
  }, [state?.customOfferModal || state?.counterOfferModal, applicant]);

  useEffect(() => {
    if (state?.customOfferModal || state?.counterOfferModal) {
      const isJobDateAvailable =
        (jobD?.startDate && jobD?.endDate) ||
        (jobD?.startDateTime && jobD?.endDateTime);
      if (isJobDateAvailable && !isEdit) {
        getStateUpdate('employerData');
      }
    }
  }, [state?.customOfferModal || state?.counterOfferModal, jobDetail]);

  // useEffect(() => {
  //   if (state?.clearModalState) {
  //     setCState({ ...stData });
  //     setState({ ...state, clearModalState: false });
  //   }
  // }, [state?.clearModalState]);

  // Only check time if the start and end dates are exactly the same
  const onSubmitHandler = () => {
    let isValid = true;

    if (isApproveType || isDeclinedType) {
      onSubmit({ ...cState, duration, salaryAmount, totalSalary });
      return true;
    } else {
      if (!startDate) {
        setCState(prevState => ({
          ...prevState,
          sDErr: true,
          sDErrTxt: translate('startDateRequired', ''),
        }));
        isValid = false;
      } else {
        setCState(prevState => ({
          ...prevState,
          sDErr: false,
          sDErrTxt: '',
        }));
      }
      if (!endDate) {
        setCState(prevState => ({
          ...prevState,
          eDErr: true,
          eDErrTxt: translate('endDateRequired', ''),
        }));
        isValid = false;
      } else {
        setCState(prevState => ({
          ...prevState,
          eDErr: false,
          eDErrTxt: '',
        }));
      }
      if (!jobDetail?.isFlatRate) {
        if (!startTime) {
          setCState(prevState => ({
            ...prevState,
            sTErr: true,
            sTErrTxt: translate('startTimeRequired', ''),
          }));
          isValid = false;
        } else {
          setCState(prevState => ({
            ...prevState,
            eTErr: false,
            sTErrTxt: '',
          }));
        }
        if (!endTime) {
          setCState(prevState => ({
            ...prevState,
            eTErr: true,
            eTErrTxt: translate('endTimeRequired', ''),
          }));
          isValid = false;
        } else {
          setCState(prevState => ({
            ...prevState,
            eTErr: false,
            eTErrTxt: '',
          }));
        }
      }
    }

    // Check for empty salary amount
    if (!salaryAmount || String(salaryAmount)?.trim()?.length === 0) {
      setCState(prevState => ({
        ...prevState,
        salaryAmountErr: true,
        salaryAmountErrTxt: translate('ammountErrorTxt', ''),
      }));
      isValid = false;
    } else {
      setCState(prevState => ({
        ...prevState,
        salaryAmountErr: false,
        salaryAmountErrTxt: '',
      }));
    }

    if (Number(salaryAmount) < minPrice) {
      checkSalary(false);
      isValid = false;
    } else {
      checkSalary(true);
    }

    // If everything is valid, call the onSubmit callback
    if (isValid) {
      onSubmit({ ...cState, duration, salaryAmount, totalSalary });
    }
  };

  const handleScroll = () => {
    if (isKeyboardVisible) {
      Keyboard.dismiss();
    }
  };

  return (
    <Modal
      visible={state?.customOfferModal || state?.counterOfferModal}
      animationType="slide"
      transparent={true}
      onRequestClose={onCancel}>
      {/* <KeyboardAvoidingView behavior="padding" style={{flex: 1, zIndex: 999}}> */}
      {/* Detects touch outside modal to close */}
      <TouchableWithoutFeedback onPress={onCancel}>
        <View style={styles.modalOverlay}>
          <TouchableWithoutFeedback>
            <View
              style={[
                styles.modalContent,
                {
                  height: isKeyboardVisible
                    ? undefined
                    : isApproveType || isDeclinedType
                      ? applicant?.message
                        ? height / 1.2
                        : height / 1.4
                      : height / 1.2,
                },
              ]}>
              <View style={styles.languageContainer}>
                <Text style={styles.languageTitle}>{title}</Text>
                {!isApproveType && !isDeclinedType && (
                  <Text style={styles.title} numberOfLines={1}>
                    {startCase(jobDetail?.title || '')}
                  </Text>
                )}
              </View>
              <KeyboardAwareScrollView onScrollBeginDrag={handleScroll}>
                <ScrollView
                  style={styles.languageContainer}
                  keyboardShouldPersistTaps="handled"
                  onScrollBeginDrag={handleScroll}
                  showsVerticalScrollIndicator={false}>
                  {isApproveType || isDeclinedType ? (
                    <>
                      <Text style={styles.titleConfirm} numberOfLines={2}>
                        {translate(
                          isDeclinedType ? 'confirmDecline' : 'confirmApprove',
                        )}
                      </Text>
                      <View style={{ marginTop: 20 }}>
                        <CounterOfferCard
                          item={applicant}
                          onActionClick={() => {}}
                          type={type === 'History' ? 'customHistory' : ''}
                          applicantsLoader={false}
                          navigation={navigation}
                          jobDetail={jobDetail}
                          buttons={false}
                        />
                        {applicant?.message ? (
                          <View style={styles.bodyDataHistorySty}>
                            <Text style={[styles.decriptionTXtSty]}>
                              {applicant?.message}
                            </Text>
                          </View>
                        ) : null}
                        {/* <View style={styles.detailBox}>
                        <View style={[styles.infoRow, styles.center]}>
                          <View style={styles.infoRow}>
                            <CustomIcon
                              name="Calander"
                              size={16}
                              color={BaseColors.logoColor}
                            />
                            <Text style={styles.jobdestxtSty}>
                              {moment(applicant?.startDate, 'MM/DD/YYYY').format(
                                'M/D/YYYY',
                              )}
                              {' - '}
                              {moment(applicant?.endDate, 'MM/DD/YYYY').format(
                                'M/D/YYYY',
                              )}
                            </Text>
                          </View>

                          <View style={styles.saleryViewSty}>
                            <Text style={styles.saleryTXtSty}>
                              {'$'}
                              {formatSalary(
                                applicant?.salaryAmount || applicant?.finalPrice,
                              )}
                              {applicant?.duration ? '/' : ''}
                              {getDuration(applicant?.duration)}
                            </Text>
                          </View>
                        </View>
                        <View style={styles.infoRow}>
                          <CustomIcon
                            name="clock"
                            size={16}
                            color={BaseColors.logoColor}
                          />
                          <Text style={styles.jobdestxtSty}>
                            {`${
                              moment(applicant?.startTime, 'hh:mm A').format(
                                'h:mm a',
                              ) || '-'
                            } - ${
                              moment(applicant?.endTime, 'hh:mm A').format(
                                'h:mm a',
                              ) || '-'
                            }`}
                          </Text>
                        </View>

                      </View> */}
                      </View>

                      <PaymentBreakdown
                        type={isEmployer ? 'employer' : 'seeker'}
                        totalSalary={
                          jobD?.totalSalaryAmount || jobD?.salaryAmount || '-'
                        }
                        serviceChargePer={
                          isEmployer
                            ? jobD?.employerServiceChargePer
                            : jobD?.seekerServiceChargePer
                        }
                        serviceCharge={
                          isEmployer
                            ? `${jobD?.serviceCharge || 0}`
                            : `${jobD?.seekerServiceCharge}` || 0
                        }
                        totalWithServiceCharge={
                          isEmployer
                            ? jobD?.totalEstimateCharge ||
                              jobD?.salaryAmount ||
                              0
                            : jobD?.seekerPayableAmount || 0
                        }
                      />
                      <View style={styles.noteSty}>
                        <FIcon
                          name="circle-dot"
                          size={12}
                          color={BaseColors.textColor}
                          style={{ paddingHorizontal: 4, paddingTop: 2.5 }}
                        />
                        <Text style={styles.noteTxtSty}>
                          {translate('payementHeld', '')}{' '}
                        </Text>
                      </View>
                    </>
                  ) : (
                    <View style={styles.durationContainer}>
                      {isCounterType && (
                        <CounterOfferCard
                          item={applicant}
                          onActionClick={() => {}}
                          type={type === 'History' ? 'customHistory' : ''}
                          applicantsLoader={false}
                          navigation={navigation}
                          jobDetail={jobDetail}
                          buttons={false}
                        />
                      )}
                      <View style={styles.rowSpaceBetween}>
                        <View style={styles.inputHalfWidth}>
                          <TextInput
                            Date={true}
                            selectedDate={startDate}
                            onDateChange={(date: any) => {
                              setCState((p: any) => ({
                                ...p,
                                startDate: date,
                                sDErr: false,
                                sDErrTxt: '',
                                endDate:
                                  endDate &&
                                  moment(date).isSameOrBefore(moment(endDate))
                                    ? endDate
                                    : date,
                              }));
                              checkAvailable(date, 'startDate');
                            }}
                            title={translate('startDate', '')}
                            minDate={new Date()}
                            datetimemodal="Start Date"
                            showError={sDErr}
                            errorText={sDErrTxt} // Show error message
                          />
                        </View>
                        <View
                          style={[
                            styles.inputHalfWidth,
                            { paddingBottom: isIOS() ? 15 : 10 },
                          ]}>
                          <TextInput
                            iseditable={startDate ? true : false} // Editable only if startDate is selected
                            Date={true}
                            selectedDate={endDate}
                            onDateChange={(date: any) => {
                              const durType = getType(
                                moment(startDate).format('MM/DD/YYYY'),
                                moment(date).format('MM/DD/YYYY'),
                              );
                              // setDuration(durType);
                              setCState((p: any) => ({
                                ...p,
                                endDate: date,
                                duration: durType,
                                eDErr: false,
                                eDErrTxt: '',
                              })); // Set the start date when changed
                              setDuration(durType);
                              calculateDur(date, durType);
                              checkAvailable(
                                date,
                                'endDate',
                                {},
                                false,
                                durType,
                              );
                            }}
                            title={translate('endDate', '')}
                            minDate={minDate}
                            datetimemodal="End Date"
                            showError={eDErr}
                            mandatory={false}
                            errorText={eDErrTxt} // Show error message
                          />
                        </View>
                      </View>
                      <View style={styles.rowSpaceBetween}>
                        <View style={styles.inputHalfWidth}>
                          <TextInput
                            Date={true}
                            selectedDate={startTime}
                            onDateChange={(time: any) => {
                              console.log('time ==>', time);
                              setCState((p: any) => ({
                                ...p,
                                startTime: time,
                                sTErr: false,
                                sTErrTxt: '',
                              }));
                              calculateDur(time, 'startTime');
                              checkAvailable(time, 'startTime');
                            }}
                            title={translate('startTime', '')}
                            dateType="time"
                            Time={true}
                            showError={sTErr}
                            errorText={sTErrTxt}
                            datetimemodal="Start Time"
                            optionalText={translate('optional')}
                          />
                        </View>
                        <View style={styles.inputHalfWidth}>
                          <TextInput
                            Date={true}
                            selectedDate={endTime}
                            onDateChange={(time: any) => {
                              // Compare startDate and endDate to check if they are the same (using .toISOString() to compare exact dates)
                              const startDateOnly = new Date(cState?.startDate)
                                .toISOString()
                                .split('T')[0]; // Extract date part
                              const endDateOnly = new Date(cState?.endDate)
                                .toISOString()
                                .split('T')[0]; // Extract date part

                              // Only check time if the start and end dates are exactly the same
                              if (startDateOnly === endDateOnly) {
                                // Check if endTime is before startTime
                                if (
                                  cState?.startTime &&
                                  time < cState?.startTime
                                ) {
                                  setCState((p: any) => ({
                                    ...p,
                                    endTime: undefined,
                                    eTErr: true,
                                    eTErrTxt: translate('endTimeError', ''),
                                  }));
                                } else {
                                  setCState((p: any) => ({
                                    ...p,
                                    endTime: time,
                                    eTErr: false,
                                    eTErrTxt: '',
                                  }));
                                }
                              } else {
                                // If the dates are not the same, allow any endTime
                                setCState((p: any) => ({
                                  ...p,
                                  endTime: time,
                                  eTErr: false,
                                  eTErrTxt: '',
                                }));
                              }
                              calculateDur(time, 'endTime');
                              checkAvailable(time, 'endTime');
                            }}
                            iseditable={startTime} // Editable only if startDate is selected
                            title={translate('endTime', '')}
                            dateType="time"
                            Time={true}
                            showError={eTErr}
                            errorText={eTErrTxt}
                            datetimemodal="End Time"
                          />
                        </View>
                      </View>

                      <View style={[styles.moreClarificationContainer]}>
                        <View style={[styles.rowSpaceBetween]}>
                          <Text style={styles.moreClarificationText}>
                            {translate('pay', '')}
                          </Text>
                        </View>

                        {jobDetail?.isFlatRate && (
                          <View
                            style={[
                              styles.rowSpaceBetween,
                              styles.center,
                              { justifyContent: 'flex-start' },
                            ]}>
                            <View style={{ paddingTop: 8 }}>
                              <SwitchComponent
                                onValueChange={(p: any) => {
                                  setCState({ ...cState, flatRate: p });
                                  // if (!p) {
                                  calculateDur('', '', {}, '');
                                  checkAvailable('', '', {}, false, '', p);
                                  // }
                                }}
                                disabled={!jobDetail?.isFlatRate}
                                value={flatRate}
                              />
                            </View>
                            <Text style={styles.flatRate}>
                              {translate('flatRate')}
                            </Text>
                          </View>
                        )}

                        <View
                          style={[
                            {
                              ...styles.rowSpaceBetween,
                              gap: 10,
                              flexDirection:
                                flatRate && jobDetail?.isFlatRate
                                  ? 'row'
                                  : 'column',
                            },
                          ]}>
                          {/* <DropdownList
                        data={
                          jobDetail?.isFlatRate
                            ? durationOptions
                            : durationOptions.filter(
                              (o: any) => o?.title !== 'Flat Rate',
                            )
                        }
                        selectedValue={duration}
                        onSelect={selectedValue => {
                          setCState((p: any) => ({
                            ...p,
                            duration: selectedValue,
                          })); // Update the duration state
                        }}
                        style={{ ...styles.inputHalfWidth }}
                        title={translate('type', '')}
                        placeholder={translate('type', '')}
                      /> */}

                          {jobDetail?.isFlatRate && flatRate ? (
                            <TextInput
                              value={`$${salaryAmount}`}
                              title={translate('payTotal', '')}
                              keyBoardType="number-pad"
                              onChange={(val: string) => {
                                const numericValue = val.replace(/[^\d.]/g, '');
                                setSalaryAmount(numericValue);
                                if (Number(numericValue) < minPrice) {
                                  checkSalary(false);
                                } else {
                                  checkSalary(true);
                                }
                                // setCState(p => ({ ...p, salaryAmount: numericValue }));
                              }}
                              maxLength={7}
                              style={{
                                backgroundColor: BaseColors.inputBackground,
                                width: '70%',
                              }}
                              placeholderText={translate('payTotal', '')}
                              showError={salaryAmountErr}
                              errorText={salaryAmountErrTxt}
                            />
                          ) : (
                            <>
                              <View
                                style={[
                                  styles.rowSpaceBetween,
                                  { paddingTop: isIOS() ? 10 : 0 },
                                ]}>
                                <View style={styles.inputHalfWidth}>
                                  <TextInput
                                    value={
                                      salaryAmount ? `$${salaryAmount}` : ''
                                    }
                                    onChange={async (val: string) => {
                                      // Remove $ sign and any non-numeric characters except decimal point
                                      const numericValue = val.replace(
                                        /[^\d.]/g,
                                        '',
                                      ); if (Number(numericValue) < minPrice) {
                                        checkSalary(false);
                                      } else {
                                        checkSalary(true);
                                      }
                                      setSalaryAmount(numericValue);
                                      calculateDur(
                                        '',
                                        duration,
                                        {},
                                        'salaryAmount',
                                      );
                                      checkAvailable(val, 'salaryAmount');
                                    }}
                                    title={translate('rate', '')}
                                    keyBoardType="number-pad"
                                    maxLength={6} // Increased to account for $ sign
                                    placeholderText={`$${translate(
                                      'addRate',
                                      '',
                                    )}`}
                                    showError={salaryAmountErr}
                                    errorText={salaryAmountErrTxt}
                                  />
                                </View>
                                <View style={styles.inputHalfWidth}>
                                  <DropdownList
                                    data={durationOptions}
                                    selectedValue={duration}
                                    onSelect={selectedValue => {
                                      setDuration(selectedValue); // Update the duration state
                                      setDurationErr({ err: false, txt: '' });
                                      checkAvailable(
                                        selectedValue,
                                        'durationType',
                                      );
                                    }}
                                    disable={true}
                                    noRightIcon={true}
                                    title={translate('type', '')}
                                    placeholder={translate('type', '')}
                                    // showError={durationErr} // Show error when `errors?.duration` exists
                                    // errorTxt={durationErrTxt} // Display error message
                                  />
                                </View>
                              </View>
                              <View
                                style={[
                                  styles.rowSpaceBetween,
                                  {
                                    paddingTop: isIOS() ? 10 : 10,
                                    paddingBottom: 10,
                                  },
                                ]}>
                                <View style={styles.inputHalfWidth}>
                                  <TextInput
                                    value={durationCal}
                                    title={translate('Duration', '')}
                                    iseditable={false}
                                    style={{
                                      backgroundColor:
                                        BaseColors.inputBackground,
                                    }}
                                    placeholderText={translate('Duration', '')}
                                  />
                                </View>
                                <View style={styles.inputHalfWidth}>
                                  <TextInput
                                    value={String(totalSalary || '')}
                                    editable={false}
                                    title={translate('payTotal', '')}
                                    keyBoardType="number-pad"
                                    iseditable={false}
                                    style={{
                                      backgroundColor:
                                        BaseColors.inputBackground,
                                    }}
                                    placeholderText={translate('payTotal', '')}
                                  />
                                </View>
                              </View>
                            </>
                          )}
                        </View>
                      </View>

                      <Text style={styles.customMsg}>
                        {translate('message', '')}
                      </Text>
                      <TextInput
                        value={msg}
                        onChange={(value: string) => {
                          setCState(p => ({ ...p, msg: value.trimStart() }));
                        }}
                        textArea={true}
                        placeholderText="Type here"
                        maxLength={1000}
                        showError={msgErr}
                        errorText={msgErrTxt}
                      />
                      <Text style={styles.briefText}>
                        {msg?.length || 0} / 1000
                      </Text>
                    </View>
                  )}
                </ScrollView>
              </KeyboardAwareScrollView>
              <View style={styles.languageButtonContainer}>
                <Button
                  onPress={() => {
                    setCState({ ...stData });
                    onCancel();
                  }}
                  disable={
                    state?.customOfferLoader || state?.counterOfferLoader
                  }
                  style={{ width: '48%' }}
                  type="outlined">
                  {translate('cancel')}
                </Button>
                <Button
                  onPress={onSubmitHandler}
                  loading={state?.customOfferLoader}
                  disable={
                    state?.customOfferLoader || state?.counterOfferLoader
                  }
                  style={{ width: '48%' }}
                  type="text">
                  {translate(
                    isApproveType
                      ? isEmployer
                        ? 'yesApprove'
                        : 'Approve'
                      : isDeclinedType
                        ? 'yesDecline'
                        : 'submit',
                  )}
                </Button>
              </View>
            </View>
          </TouchableWithoutFeedback>
        </View>
      </TouchableWithoutFeedback>
      {/* </KeyboardAvoidingView> */}
    </Modal>
  );
});
