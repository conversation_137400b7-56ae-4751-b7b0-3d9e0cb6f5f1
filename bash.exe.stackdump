Stack trace:
Frame         Function      Args
0007FFFF9F90  00021005FEBA (000210285F48, 00021026AB6E, 000000000000, 0007FFFF8E90) msys-2.0.dll+0x1FEBA
0007FFFF9F90  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFA268) msys-2.0.dll+0x67F9
0007FFFF9F90  000210046832 (000210285FF9, 0007FFFF9E48, 000000000000, 000000000000) msys-2.0.dll+0x6832
0007FFFF9F90  000210068F86 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28F86
0007FFFF9F90  0002100690B4 (0007FFFF9FA0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x290B4
0007FFFFA270  00021006A49D (0007FFFF9FA0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A49D
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFF97BE0000 ntdll.dll
7FFF967F0000 KERNEL32.DLL
7FFF95210000 KERNELBASE.dll
7FFF969E0000 USER32.dll
7FFF95950000 win32u.dll
000210040000 msys-2.0.dll
7FFF97990000 GDI32.dll
7FFF95810000 gdi32full.dll
7FFF95160000 msvcp_win.dll
7FFF956C0000 ucrtbase.dll
7FFF96F90000 advapi32.dll
7FFF96640000 msvcrt.dll
7FFF96740000 sechost.dll
7FFF968C0000 RPCRT4.dll
7FFF94450000 CRYPTBASE.DLL
7FFF950C0000 bcryptPrimitives.dll
7FFF95E20000 IMM32.DLL
